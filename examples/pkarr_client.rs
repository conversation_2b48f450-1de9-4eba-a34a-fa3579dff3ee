use anyhow::Result;
use ed25519_dalek::VerifyingKey;
use iroh_topic_tracker::integrations::pkarr::{PkarrClient, PublicExtendedKey};
use pkarr::PublicKey;

// For public key derivation, we need these imports
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT, edwards::CompressedEdwardsY};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};

type HmacSha512 = Hmac<Sha512>;

const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    InvalidChildIndex,
    CryptoError,
    KeyAtInfinity,
    HardenedDerivationNotSupported,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::InvalidChildIndex => write!(f, "Invalid child index"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
            KeyDerivationError::KeyAtInfinity => write!(f, "Key at infinity"),
            KeyDerivationError::HardenedDerivationNotSupported => write!(f, "Hardened derivation not supported for public keys"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

// Public key derivation functionality for client-side use
pub struct PublicKeyDerivation;

impl PublicKeyDerivation {
    /// Derive child public key at given index (non-hardened only)
    pub fn derive_child(extended_key: &PublicExtendedKey, index: u32) -> Result<PublicExtendedKey, KeyDerivationError> {
        if index >= HARDENED_OFFSET {
            println!("InvalidChildIndex");
            return Err(KeyDerivationError::InvalidChildIndex);
        }

        // Non-hardened derivation: 0x02 || A || index
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(extended_key.public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());

        // Compute HMAC
        let mut mac = HmacSha512::new_from_slice(&extended_key.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();

        // Split result
        let mut kl_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);

        // Check third highest bit requirement
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }

        // Apply Ed25519 bit manipulations
        Self::clamp_scalar(&mut kl_new);

        // Generate child scalar
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);

        // For public key derivation: child_public = parent_public + child_scalar * G
        let compressed_parent = CompressedEdwardsY(*extended_key.public_key.as_bytes());
        let parent_point = compressed_parent.decompress()
            .ok_or(KeyDerivationError::CryptoError)?;
        let child_public_point = parent_point + &child_scalar * &ED25519_BASEPOINT_POINT;

        // Check if public key is identity point (should discard)
        if child_public_point.compress().to_bytes() == [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] {
            return Err(KeyDerivationError::KeyAtInfinity);
        }

        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;

        // Generate child chain code using the same deterministic logic
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; 32];
        child_chain_code.copy_from_slice(&chain_code_hash);

        Ok(PublicExtendedKey::new(child_public_key, child_chain_code))
    }

    /// Apply Ed25519 scalar clamping
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;  // Clear bottom 3 bits
        scalar[31] &= 127; // Clear top bit
        scalar[31] |= 64;  // Set second highest bit
    }
}

/// Deterministic key finder for client-side public key derivation
pub struct DeterministicPublicKeyFinder;

impl DeterministicPublicKeyFinder {
    /// Find the first valid public key index for a given query string
    pub fn find_valid_public_key(
        extended_key: &PublicExtendedKey,
        query: &str,
        max_attempts: Option<u32>,
    ) -> Result<(u32, PublicExtendedKey), KeyDerivationError> {
        let max_attempts = max_attempts.unwrap_or(1000);

        // Hash the query string to get a deterministic starting point (same as server)
        let mut hasher = Sha256::new();
        hasher.update(query.as_bytes());
        let hash = hasher.finalize();

        // Use first 4 bytes of hash as starting index
        // then fit space in HARDENED_OFFSET
        let start_index = u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]]) % HARDENED_OFFSET;

        // Try indices starting from the hash-derived index
        for attempt in 0..max_attempts {
            let index = start_index.wrapping_add(attempt);

            match PublicKeyDerivation::derive_child(extended_key, index) {
                Ok(key) => {
                    println!("   Found valid public key for '{}' at index {} (attempt {})", query, index, attempt + 1);
                    return Ok((index, key));
                }
                Err(KeyDerivationError::InvalidKey) => {
                    // Continue to next index
                    continue;
                }
                Err(e) => {
                    // Other errors are not retryable
                    println!("   Error deriving public key for '{}' at index {}: {:?}", query, index, e);
                    return Err(e);
                }
            }
        }

        Err(KeyDerivationError::InvalidKey)
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Pkarr Query Key Resolution ===");

    // Step 1: Resolve the query master public key from pkarr
    let pkarr_public_key_str = "yfkhkpzsgbk7myjkrj3z16juy9bgd8ztft16w9r438p3zdxcugdo"; // From latest hierarchical_pkarr run
    let pkarr_public_key = PublicKey::try_from(pkarr_public_key_str)?;

    println!("1. Using pkarr public key: {}", pkarr_public_key.to_z32());

    let pkarr_client = PkarrClient::new()?;

    // Step 2: Resolve the query master public key
    let query_master_public_key = match pkarr_client.resolve_query_key(&pkarr_public_key, Some("query-master")).await {
        Ok(key) => {
            println!("2. ✓ Resolved query master public key from pkarr");
            println!("   Query master key: {}", hex::encode(key.as_bytes()));
            key
        }
        Err(e) => {
            println!("2. ✗ Failed to resolve query master key: {}", e);
            println!("   Run the hierarchical_pkarr example first to publish the key");
            return Ok(());
        }
    };

    // Step 3: Create the same ExtendedKey structure that the server uses for purpose derivation
    // Both server and client use the same deterministic chain code for deriving purpose keys
    let query_master_extended = PublicExtendedKey::from_query_master_and_query(query_master_public_key, "purpose-derivation");

    println!("3. Created extended key with deterministic chain code for purpose derivation");
    println!("   Chain code: {}", hex::encode(&query_master_extended.chain_code));

    // Step 4: Derive specific query keys for different purposes using deterministic method
    let purposes = vec![
        "user-data",
        "messages",
        "metadata",
        "files",
        "social",
    ];

    println!("4. Deriving specific query keys (deterministic, same as server):");

    for purpose in purposes {
        match DeterministicPublicKeyFinder::find_valid_public_key(
            &query_master_extended,
            purpose,
            None
        ) {
            Ok((index, derived_key)) => {
                println!("   ✓ {}: m/{} → {}",
                    purpose,
                    index,
                    hex::encode(derived_key.public_key.as_bytes())[..16].to_string() + "..."
                );

                // Now the client can use this derived public key to resolve pkarr records
                // For example: resolve pkarr records published under this derived key
                println!("     → Can resolve pkarr records for {}", purpose);
            }
            Err(e) => {
                println!("   ✗ {}: Failed to derive - {}", purpose, e);
            }
        }
    }

    println!("\n=== Security Analysis ===");
    println!("✓ Server can derive private keys for all purposes (has master private key)");
    println!("✓ Client can derive public keys for all purposes (has master public key)");
    println!("✓ Client uses SAME deterministic derivation as server (identical indices)");
    println!("✗ Client CANNOT derive private keys (hardened derivation protects master)");
    println!("✗ Client CANNOT overwrite pkarr records (no private keys for derived purposes)");

    println!("\n=== Use Case Example ===");
    println!("1. Server publishes user data under derived key for 'user-data' (m/0)");
    println!("2. Server publishes messages under derived key for 'messages' (m/1)");
    println!("3. Client derives same public keys and resolves both record types");
    println!("4. Client cannot modify any records (no private keys)");

    // Demonstrate that we can derive many keys without the third-bit issue
    println!("\n=== Demonstrating Multiple Derivations ===");
    let mut successful_derivations = 0;
    for i in 0..20 {
        match PublicKeyDerivation::derive_child(&query_master_extended, i) {
            Ok(_) => {
                successful_derivations += 1;
            }
            Err(_) => {
                // Skip invalid keys due to third highest bit
            }
        }
    }
    println!("Successfully derived {}/20 keys (some skipped due to third highest bit rule)", successful_derivations);

    // Add a pkarr resolve query to prove it works
    println!("\n=== Proving Pkarr Resolution Works ===");

    // Try to resolve a specific purpose key that should have been published by the server
    if let Ok((user_data_index, user_data_key)) = DeterministicPublicKeyFinder::find_valid_public_key(
        &query_master_extended,
        "user-data",
        None
    ) {
        println!("1. Derived 'user-data' key at index {}", user_data_index);

        // Convert the derived Ed25519 public key to a pkarr PublicKey for resolution
        if let Ok(user_data_pkarr_key) = pkarr::PublicKey::try_from(&user_data_key.public_key.to_bytes()) {
            println!("2. Converted to pkarr public key: {}", user_data_pkarr_key.to_z32());

            // Try to resolve any records published under this derived key
            match pkarr_client.resolve_query_key(&user_data_pkarr_key, Some("info")).await {
                Ok(resolved_info_key) => {
                    println!("3. ✓ Successfully resolved 'info' record from derived key!");
                    println!("   Resolved key: {}", hex::encode(resolved_info_key.as_bytes()));
                    println!("   This proves the hierarchical derivation and pkarr resolution works end-to-end!");
                }
                Err(e) => {
                    println!("3. ✗ No 'info' record found under derived key: {}", e);
                    println!("   This is expected if the server hasn't published data under this specific key");
                }
            }
        } else {
            println!("2. ✗ Failed to convert Ed25519 key to pkarr PublicKey");
        }
    } else {
        println!("1. ✗ Failed to derive 'user-data' key");
    }

    // Also try resolving from a known published key from the server
    println!("\n=== Testing Resolution from Known Server Key ===");

    // This is the pkarr key that the server published for 'user-data' purpose
    let known_server_key = "qd3516qkhq695tib8rcc381pocsdizhkppuo5a9mbdtmwx656ogo"; // From latest server run
    if let Ok(server_pkarr_key) = pkarr::PublicKey::try_from(known_server_key) {
        println!("1. Trying to resolve from known server key: {}", known_server_key);

        match pkarr_client.resolve_query_key(&server_pkarr_key, Some("info")).await {
            Ok(resolved_key) => {
                println!("2. ✓ Successfully resolved 'info' record from server's published key!");
                println!("   Resolved key: {}", hex::encode(resolved_key.as_bytes()));
                println!("   🎉 This proves the complete hierarchical pkarr workflow works!");
            }
            Err(e) => {
                println!("2. ✗ Failed to resolve from server key: {}", e);
                println!("   The server record may have expired or not propagated yet");
            }
        }
    }

    Ok(())
}
