use anyhow::{anyhow, Result};
use ed25519_dalek::{ed25519::signature::<PERSON><PERSON><PERSON><PERSON>, <PERSON>ing<PERSON><PERSON>, Verifying<PERSON><PERSON>};
use hmac::{Hmac, <PERSON>};
use sha2::{Digest, Sha256, Sha512};
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT, edwards::CompressedEdwardsY};
use rand::{rngs::OsRng, Rng as _};

type HmacSha512 = Hmac<Sha512>;
type HmacSha256 = Hmac<Sha256>;

#[derive(Debug, <PERSON>lone)]
struct HierarchicalKey {
    // BIP32-Ed25519 uses extended private key format
    extended_private_key: [u8; 64],  // kL (32 bytes) || kR (32 bytes)
    public_key: VerifyingKey,
    chain_code: [u8; 32],
}

impl HierarchicalKey {
    fn from_seed(seed: &[u8]) -> Result<Self> {
        // BIP32-Ed25519: derive extended private key
        let mut mac = HmacSha512::new_from_slice(b"ed25519 seed")?;
        mac.update(seed);
        let result = mac.finalize().into_bytes();
        
        let mut extended_private_key = [0u8; 64];
        extended_private_key.copy_from_slice(&result);
        
        // Extract kL and kR
        let mut kL = [0u8; 32];
        let mut kR = [0u8; 32];
        kL.copy_from_slice(&extended_private_key[..32]);
        kR.copy_from_slice(&extended_private_key[32..]);
        
        // BIP32-Ed25519: Check third highest bit of last byte of kL
        if (kL[31] & 0x20) != 0 {
            return Err(anyhow!("Invalid root key: third highest bit set"));
        }
        
        // BIP32-Ed25519: Set required bits in kL
        Self::clamp_kL(&mut kL);
        
        // Update extended private key
        extended_private_key[..32].copy_from_slice(&kL);
        extended_private_key[32..].copy_from_slice(&kR);
        
        // Generate public key from kL
        let scalar = Scalar::from_bytes_mod_order(kL);
        let public_point = scalar * ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())?;
        
        // BIP32-Ed25519: Generate chain code
        let mut chain_mac = HmacSha256::new_from_slice(&[0x01])?;
        chain_mac.update(&result);
        let binding = chain_mac.finalize().into_bytes();
        let chain_result = binding.as_slice();
        let mut chain_code = [0u8; 32];
        chain_code.copy_from_slice(&chain_result);
        
        Ok(HierarchicalKey {
            extended_private_key,
            public_key,
            chain_code,
        })
    }
    
    fn derive_hardened_child(&self, index: u32) -> Result<Self> {
        // BIP32-Ed25519: Z ← FcP(0x00||kP||i) for hardened
        let mut hmac_input = Vec::new();
        hmac_input.push(0x00);
        hmac_input.extend_from_slice(&self.extended_private_key);
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)?;
        mac.update(&hmac_input);
        let Z = mac.finalize().into_bytes();
        
        // BIP32-Ed25519: Split Z into ZL (28 bytes) and ZR (32 bytes)
        let ZL = &Z[..28];
        let ZR = &Z[32..];
        
        // Get parent kL and kR
        let parent_kL: &[u8; 32] = &self.extended_private_key[..32].try_into().unwrap();
        let parent_kR = &self.extended_private_key[32..];
        
        // BIP32-Ed25519: kL ← 8[ZL]+[kP_L]
        let ZL_scalar = Scalar::from_bytes_mod_order_wide(&Self::pad_28_to_64(ZL));
        let eight_ZL = ZL_scalar * Scalar::from(8u64);
        let parent_kL_scalar = Scalar::from_bytes_mod_order(*parent_kL);
        let child_kL_scalar = eight_ZL + parent_kL_scalar;
        let child_kL = child_kL_scalar.to_bytes();
        
        // BIP32-Ed25519: kR ← [ZR]+[kP_R] mod 2^256
        let mut child_kR = [0u8; 32];
        let ZR_num = Self::bytes_to_u256(ZR);
        let parent_kR_num = Self::bytes_to_u256(parent_kR);
        let child_kR_num = Self::add_u256(ZR_num, parent_kR_num);
        Self::u256_to_bytes(child_kR_num, &mut child_kR);
        
        // Create child extended private key
        let mut child_extended_private_key = [0u8; 64];
        child_extended_private_key[..32].copy_from_slice(&child_kL);
        child_extended_private_key[32..].copy_from_slice(&child_kR);
        
        // Generate child public key
        let child_scalar = Scalar::from_bytes_mod_order(child_kL);
        let child_public_point = child_scalar * ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())?;
        
        // BIP32-Ed25519: Generate child chain code
        let mut chain_mac = HmacSha256::new_from_slice(&[0x01])?;
        chain_mac.update(&Z);
        let binding = chain_mac.finalize().into_bytes();
        let chain_result = binding.as_slice();
        let mut child_chain_code = [0u8; 32];
        child_chain_code.copy_from_slice(&chain_result);
        
        Ok(HierarchicalKey {
            extended_private_key: child_extended_private_key,
            public_key: child_public_key,
            chain_code: child_chain_code,
        })
    }
    
    fn derive_nonhardened_child(&self, index: u32) -> Result<Self> {
        // BIP32-Ed25519: Z ← FcP(0x02||AP||i) for non-hardened
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(self.public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)?;
        mac.update(&hmac_input);
        let Z = mac.finalize().into_bytes();
        
        // BIP32-Ed25519: Split Z into ZL (28 bytes) and ZR (32 bytes)
        let ZL = &Z[..28];
        let ZR = &Z[32..];
        
        // Get parent kL and kR
        let parent_kL: &[u8; 32] = &self.extended_private_key[..32].try_into().unwrap();
        let parent_kR = &self.extended_private_key[32..];
        
        // BIP32-Ed25519: kL ← 8[ZL]+[kP_L]
        let ZL_scalar = Scalar::from_bytes_mod_order_wide(&Self::pad_28_to_64(ZL));
        let eight_ZL = ZL_scalar * Scalar::from(8u64);
        let parent_kL_scalar = Scalar::from_bytes_mod_order(*parent_kL);
        let child_kL_scalar = eight_ZL + parent_kL_scalar;
        let child_kL = child_kL_scalar.to_bytes();
        
        // BIP32-Ed25519: kR ← [ZR]+[kP_R] mod 2^256
        let mut child_kR = [0u8; 32];
        let ZR_num = Self::bytes_to_u256(ZR);
        let parent_kR_num = Self::bytes_to_u256(parent_kR);
        let child_k_r_num = Self::add_u256(ZR_num, parent_kR_num);
        Self::u256_to_bytes(child_k_r_num, &mut child_kR);
        
        // Create child extended private key
        let mut child_extended_private_key = [0u8; 64];
        child_extended_private_key[..32].copy_from_slice(&child_kL);
        child_extended_private_key[32..].copy_from_slice(&child_kR);
        
        // Generate child public key
        let child_scalar = Scalar::from_bytes_mod_order(child_kL);
        let child_public_point = child_scalar * ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())?;
        
        // BIP32-Ed25519: Generate child chain code
        let mut chain_mac = HmacSha256::new_from_slice(&[0x01])?;
        chain_mac.update(&Z);
        let binding = chain_mac.finalize().into_bytes();
        let chain_result = binding.as_slice();
        let mut child_chain_code = [0u8; 32];
        child_chain_code.copy_from_slice(&chain_result);
        
        Ok(HierarchicalKey {
            extended_private_key: child_extended_private_key,
            public_key: child_public_key,
            chain_code: child_chain_code,
        })
    }
    
    // BIP32-Ed25519: Public key derivation for clients
    fn derive_public_key_only(
        parent_public_key: &VerifyingKey,
        parent_chain_code: &[u8; 32],
        index: u32,
    ) -> Result<VerifyingKey> {
        // BIP32-Ed25519: Z ← FcP(0x02||AP||i) for non-hardened
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(parent_public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(parent_chain_code)?;
        mac.update(&hmac_input);
        let Z = mac.finalize().into_bytes();
        
        // BIP32-Ed25519: Split Z into ZL (28 bytes) and ZR (32 bytes)
        let ZL = &Z[..28];
        
        // BIP32-Ed25519: Ai ← AP + [8ZL]B
        let parent_point = CompressedEdwardsY(*parent_public_key.as_bytes())
            .decompress()
            .ok_or_else(|| anyhow!("Invalid parent public key"))?;
        
        let ZL_scalar = Scalar::from_bytes_mod_order_wide(&Self::pad_28_to_64(ZL));
        let eight_ZL = ZL_scalar * Scalar::from(8u64);
        let offset_point = eight_ZL * ED25519_BASEPOINT_POINT;
        let child_point = parent_point + offset_point;
        
        let child_public_key = VerifyingKey::from_bytes(&child_point.compress().to_bytes())?;
        
        Ok(child_public_key)
    }
    
    // BIP32-Ed25519: Clamp kL according to spec
    fn clamp_kL(kL: &mut [u8; 32]) {
        // Clear lowest 3 bits of first byte
        kL[0] &= 0xF8;
        // Clear highest bit of last byte
        kL[31] &= 0x7F;
        // Set second highest bit of last byte
        kL[31] |= 0x40;
    }
    
    // Convert 28-byte ZL to 64-byte array for wide scalar conversion
    fn pad_28_to_64(bytes_28: &[u8]) -> [u8; 64] {
        let mut result = [0u8; 64];
        result[..28].copy_from_slice(bytes_28);
        result
    }
    
    // Helper functions for 256-bit arithmetic
    fn bytes_to_u256(bytes: &[u8]) -> [u64; 4] {
        let mut result = [0u64; 4];
        for (i, chunk) in bytes.chunks(8).enumerate() {
            if i < 4 {
                let mut padded = [0u8; 8];
                padded[..chunk.len()].copy_from_slice(chunk);
                result[i] = u64::from_le_bytes(padded);
            }
        }
        result
    }
    
    fn u256_to_bytes(num: [u64; 4], bytes: &mut [u8; 32]) {
        for (i, &word) in num.iter().enumerate() {
            let word_bytes = word.to_le_bytes();
            bytes[i*8..(i+1)*8].copy_from_slice(&word_bytes);
        }
    }

    fn add_u256(a: [u64; 4], b: [u64; 4]) -> [u64; 4] {
        let mut result = [0u64; 4];
        let mut carry = 0u64;

        for i in 0..4 {
            let (sum, overflow1) = a[i].overflowing_add(b[i]);
            let (sum_with_carry, overflow2) = sum.overflowing_add(carry);
            result[i] = sum_with_carry;
            carry = if overflow1 || overflow2 { 1 } else { 0 };
        }

        result
    }
    
    fn get_deterministic_index(purpose: &str) -> u32 {
        let mut hasher = Sha256::new();
        hasher.update(purpose.as_bytes());
        let hash = hasher.finalize();
        u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]])
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== BIP32-Ed25519 Hierarchical Key Derivation Pipeline ===\n");
    
    // =============================================
    // STEP 1: Create Root Keypair
    // =============================================
    println!("📍 STEP 1: Create Root Keypair");
    
    let mut seed = [0u8; 32];
    OsRng.fill(&mut seed);
    
    let root_key = HierarchicalKey::from_seed(&seed)?;
    
    println!("   Root Extended Private: {}", hex::encode(&root_key.extended_private_key)[..32].to_string() + "...");
    println!("   Root Public Key:       {}", hex::encode(root_key.public_key.as_bytes()));
    println!("   Root Chain Code:       {}", hex::encode(&root_key.chain_code)[..16].to_string() + "...");
    
    // =============================================
    // STEP 2: Hardened Derive Query Master Key
    // =============================================
    println!("\n📍 STEP 2: Hardened Derive Query Master Key");
    
    let query_master_index = HierarchicalKey::get_deterministic_index("query-master") | 0x80000000;
    let query_master_key = root_key.derive_hardened_child(query_master_index)?;
    
    println!("   Query Master Index:    0x{:08X} (hardened)", query_master_index);
    println!("   Query Master Extended: {}", hex::encode(&query_master_key.extended_private_key)[..32].to_string() + "...");
    println!("   Query Master Public:   {}", hex::encode(query_master_key.public_key.as_bytes()));
    println!("   Query Master Chain:    {}", hex::encode(&query_master_key.chain_code)[..16].to_string() + "...");
    
    // =============================================
    // STEP 3: Non-Hardened Derive Purpose Key (Method 1: From Private Key)
    // =============================================
    println!("\n📍 STEP 3: Non-Hardened Derive Purpose Key (Method 1: From Private Key)");
    
    let purpose = "user-data";
    let purpose_index = HierarchicalKey::get_deterministic_index(purpose) & 0x7FFFFFFF;
    let purpose_key_method1 = query_master_key.derive_nonhardened_child(purpose_index)?;
    
    println!("   Purpose: '{}'", purpose);
    println!("   Purpose Index:     0x{:08X} (non-hardened)", purpose_index);
    println!("   Purpose Extended:  {}", hex::encode(&purpose_key_method1.extended_private_key)[..32].to_string() + "...");
    println!("   Purpose Public:    {}", hex::encode(purpose_key_method1.public_key.as_bytes()));
    
    // =============================================
    // STEP 4: Non-Hardened Derive Purpose Key (Method 2: From Public Key Only)
    // =============================================
    println!("\n📍 STEP 4: Non-Hardened Derive Purpose Key (Method 2: From Public Key Only)");
    
    let purpose_public_key_method2 = HierarchicalKey::derive_public_key_only(
        &query_master_key.public_key,
        &query_master_key.chain_code,
        purpose_index,
    )?;
    
    println!("   Purpose: '{}'", purpose);
    println!("   Purpose Index:     0x{:08X} (non-hardened)", purpose_index);
    println!("   Purpose Extended:  [UNKNOWN - Client cannot derive]");
    println!("   Purpose Public:    {}", hex::encode(purpose_public_key_method2.as_bytes()));
    
    // =============================================
    // STEP 5: Assert Equality - The Math Works!
    // =============================================
    println!("\n📍 STEP 5: Assert Equality - The Math Works!");
    
    let public_key_1 = purpose_key_method1.public_key.as_bytes();
    let public_key_2 = purpose_public_key_method2.as_bytes();
    
    println!("   🔍 Detailed Comparison:");
    println!("   Method 1 (private → public): {}", hex::encode(public_key_1));
    println!("   Method 2 (public → public):  {}", hex::encode(public_key_2));
    
    assert_eq!(public_key_1, public_key_2, "Public keys must match!");
    
    println!("   ✅ Public Key Match: Both methods produce identical public keys");
    println!("   ✅ BIP32-Ed25519 Math: (parent_kL + 8×ZL) × G == parent_public + (8×ZL) × G");
    
    // =============================================
    // STEP 6: Demonstrate the BIP32-Ed25519 Foundation
    // =============================================
    println!("\n📍 STEP 6: Demonstrate the BIP32-Ed25519 Foundation");
    
    println!("   🧮 BIP32-Ed25519 Key Components:");
    println!("      • Extended Private Key = kL (32 bytes) || kR (32 bytes)");
    println!("      • kL = clamped scalar for signing");
    println!("      • kR = additional entropy for signatures");
    println!("      • Public Key = kL × G");
    println!("");
    println!("   🔑 Child Derivation (Non-Hardened):");
    println!("      • Z = HMAC-SHA512(chain_code, 0x02 || parent_public || index)");
    println!("      • ZL = Z[0..28] (28 bytes), ZR = Z[32..64] (32 bytes)");
    println!("      • Method 1: child_kL = parent_kL + 8×ZL, child_public = child_kL × G");
    println!("      • Method 2: child_public = parent_public + (8×ZL) × G");
    println!("      • Both give same result: (parent_kL + 8×ZL) × G");
    
    // =============================================
    // STEP 7: Security Properties
    // =============================================
    println!("\n📍 STEP 7: Security Properties");
    
    println!("   🔒 Server Powers (has extended private keys):");
    println!("      • ✅ Can derive query master extended private key (has root extended private)");
    println!("      • ✅ Can derive purpose extended private keys (has query master extended private)");
    println!("      • ✅ Can sign EdDSA signatures with any derived key");
    println!("      • ✅ Can publish/modify DNS records");
    
    println!("   🔓 Client Limitations (has public keys only):");
    println!("      • ❌ Cannot derive query master private key (hardened derivation)");
    println!("      • ❌ Cannot derive purpose private keys (no parent extended private)");
    println!("      • ✅ Can derive purpose public keys (non-hardened derivation)");
    println!("      • ✅ Can verify EdDSA signatures and resolve records");
    println!("      • ❌ Cannot forge signatures or modify records");
    
    // =============================================
    // STEP 8: Key Hierarchy Visualization
    // =============================================
    println!("\n📍 STEP 8: Key Hierarchy Visualization");
    
    println!("   BIP32-Ed25519 Derivation Path:");
    println!("   m (root seed)");
    println!("   └─ m/0x{:08X}' (query-master, hardened)", query_master_index);
    println!("      └─ m/0x{:08X}'/0x{:08X} (user-data, non-hardened)", query_master_index, purpose_index);
    println!("");
    println!("   Derivation Types:");
    println!("   • Hardened (') = Requires extended private key, uses 0x00 prefix");
    println!("   • Non-hardened = Can use public key, uses 0x02 prefix");
    println!("   • ZL multiplication by 8 ensures proper bit alignment");
    
    // =============================================
    // STEP 9: Demonstrate EdDSA Signing
    // =============================================
    println!("\n📍 STEP 9: Demonstrate EdDSA Signing");
    
    let message = b"Hello, BIP32-Ed25519!";
    
    // Create signing key from extended private key
    let kL = &purpose_key_method1.extended_private_key[..32];
    let kR = &purpose_key_method1.extended_private_key[32..];
    
    println!("   📝 Server signs message: '{}'", String::from_utf8_lossy(message));
    println!("   📝 Using kL: {}", hex::encode(kL)[..16].to_string() + "...");
    println!("   📝 Using kR: {}", hex::encode(kR)[..16].to_string() + "...");
    
    // For simplicity, use the SigningKey from kL (real EdDSA would use both kL and kR)
    let kL: [u8; 32] = kL.try_into().unwrap(); 
    let mut signing_key = SigningKey::from_bytes(&kL);
    let signature = signing_key.sign(message);
    
    println!("   📝 Signature: {}", hex::encode(signature.to_bytes())[..32].to_string() + "...");
    
    // Client verifies using derived public key
    let verification_result = purpose_public_key_method2.verify_strict(message, &signature);
    
    println!("   ✅ Client verifies with derived public key: {:?}", verification_result.is_ok());
    println!("   📋 Client cannot sign (no extended private key)");
    
    println!("\n✅ BIP32-Ed25519 Pipeline Complete!");
    println!("   The server can derive all extended private keys, the client can only derive public keys.");
    println!("   This follows the BIP32-Ed25519 specification for hierarchical deterministic keys.");
    
    Ok(())
}