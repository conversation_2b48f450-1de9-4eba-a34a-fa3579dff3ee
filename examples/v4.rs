use curve25519_dalek::{scalar::<PERSON>ala<PERSON>, traits::Identity};
use curve25519_dalek::edwards::{EdwardsPoint, CompressedEdwardsY};
use curve25519_dalek::constants::ED25519_BASEPOINT_POINT;
use ed25519_dalek::Verifying<PERSON>ey;
use hmac::{Hmac, Mac};
use sha2::{Sha256, Sha512, Digest};
use pkarr::{PublicKey as Pkarr<PERSON>ub<PERSON><PERSON><PERSON>, SignedPacket, Keypair};
use serde::{Serialize, Deserialize};
use z32;

type HmacSha512 = Hmac<Sha512>;

// Raw Ed25519 signature implementation
#[derive(Debug, Clone)]
pub struct RawSignature {
    pub r: [u8; 32],
    pub s: [u8; 32],
}

impl RawSignature {
    pub fn to_bytes(&self) -> [u8; 64] {
        let mut bytes = [0u8; 64];
        bytes[..32].copy_from_slice(&self.r);
        bytes[32..].copy_from_slice(&self.s);
        bytes
    }
}

// BIP32-Ed25519 implementation (keeping it focused)
#[derive(Clone)]
struct ExtendedPrivateKey {
    kl: [u8; 32],
    kr: [u8; 32],
    chain_code: [u8; 32],
}

#[derive(Clone)]
struct ExtendedPublicKey {
    public_key: VerifyingKey,
    chain_code: [u8; 32],
}

#[derive(Serialize, Deserialize)]
struct RootRecord {
    chain_code: String, // hex encoded
    version: u8,
}

impl ExtendedPrivateKey {
    fn generate() -> Self {
        let seed: [u8; 32] = rand::random();
        Self::from_seed(&seed)
    }
    
    fn from_seed(seed: &[u8]) -> Self {
        loop {
            let mut hasher = Sha512::new();
            hasher.update(seed);
            hasher.update(&rand::random::<[u8; 4]>());
            let k = hasher.finalize();
            
            let mut kl = [0u8; 32];
            let mut kr = [0u8; 32];
            kl.copy_from_slice(&k[..32]);
            kr.copy_from_slice(&k[32..]);
            
            if (kl[31] & 0x20) != 0 {
                continue;
            }
            
            kl[0] &= 0xF8;
            kl[31] &= 0x7F;
            kl[31] |= 0x40;
            
            let public_key = {
                let scalar = Scalar::from_bytes_mod_order(kl);
                let point = scalar * ED25519_BASEPOINT_POINT;
                VerifyingKey::from_bytes(&point.compress().as_bytes()).unwrap()
            };

            let mut chain_hasher = Sha256::new();
            chain_hasher.update(b"BIP32-Ed25519-chain");
            chain_hasher.update(&public_key.to_bytes());
            let chain_code = chain_hasher.finalize().into();
            
            return ExtendedPrivateKey { kl, kr, chain_code };
        }
    }
    
    fn derive_child(&self, index: u32) -> Option<ExtendedPrivateKey> {
        if index >= (1 << 31) {
            return None;
        }
        
        let public_key = self.public_key();
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(&public_key.to_bytes());
        hmac_input.extend_from_slice(&index.to_le_bytes());
        
        let mut hmac = HmacSha512::new_from_slice(&self.chain_code).ok()?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();
        
        let zl = &z[..28];
        let zr = &z[32..];
        
        let mut kl_child = [0u8; 32];
        kl_child[..28].copy_from_slice(zl);
        
        let mut carry = 0u8;
        for i in 0..32 {
            let temp = (kl_child[i] as u16) << 3 | carry as u16;
            kl_child[i] = temp as u8;
            carry = (temp >> 8) as u8;
        }
        
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = kl_child[i] as u16 + self.kl[i] as u16 + carry;
            kl_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        let mut kr_child = [0u8; 32];
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = zr[i] as u16 + self.kr[i] as u16 + carry;
            kr_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        let scalar = Scalar::from_bytes_mod_order(kl_child);
        if scalar == Scalar::ZERO {
            return None;
        }
        
        Some(ExtendedPrivateKey {
            kl: kl_child,
            kr: kr_child,
            chain_code: self.chain_code,
        })
    }
    
    fn public_key(&self) -> VerifyingKey {
        let scalar = Scalar::from_bytes_mod_order(self.kl);
        let point = scalar * ED25519_BASEPOINT_POINT;
        VerifyingKey::from_bytes(&point.compress().as_bytes()).unwrap()
    }
    
    fn to_pkarr_public_key(&self) -> PkarrPublicKey {
        PkarrPublicKey::from(self.public_key())
    }

    fn public(&self) -> ExtendedPublicKey {
        ExtendedPublicKey {
            public_key: self.public_key(),
            chain_code: self.chain_code,
        }
    }
    
    // RAW Ed25519 signature that matches pkarr's expectations
    fn sign_raw(&self, message: &[u8]) -> RawSignature {
        // Step 1: Compute r = hash(kR || M) mod q
        let mut hasher = Sha512::new();
        hasher.update(&self.kr);
        hasher.update(message);
        let r_hash = hasher.finalize();
        let r_scalar = Scalar::from_bytes_mod_order_wide(&r_hash.try_into().unwrap());
        
        // Step 2: Compute R = r * B
        let r_point = r_scalar * ED25519_BASEPOINT_POINT;
        let r_bytes = r_point.compress().as_bytes().clone();
        
        // Step 3: Compute S = (r + hash(R || A || M) * s) mod q
        let public_key = self.public_key();
        let mut hasher = Sha512::new();
        hasher.update(&r_bytes);
        hasher.update(&public_key.to_bytes());
        hasher.update(message);
        let k_hash = hasher.finalize();
        let k_scalar = Scalar::from_bytes_mod_order_wide(&k_hash.try_into().unwrap());
        
        let s_scalar = Scalar::from_bytes_mod_order(self.kl);
        let s_final = r_scalar + (k_scalar * s_scalar);
        
        RawSignature {
            r: r_bytes,
            s: s_final.as_bytes().clone(),
        }
    }
    
    // Create a pkarr keypair for publishing (note: this uses different key expansion than ours)
    // This is just for demonstration - in practice you'd want to use our manual signing
    fn to_pkarr_keypair_for_demo(&self) -> pkarr::Keypair {
        // For demo purposes, create a signing key from our kl
        // NOTE: This will use pkarr's key expansion, not our BIP32-Ed25519 expansion
        let signing_key = ed25519_dalek::SigningKey::from_bytes(&self.kl);
        pkarr::Keypair::from_secret_key(&signing_key.to_bytes())
    }

    // Demonstrate our manual signature on a message
    fn demonstrate_manual_signature(&self, message: &[u8]) -> RawSignature {
        println!("� Demonstrating manual Ed25519 signature:");
        println!("  - Message: {}", hex::encode(message));

        let signature = self.sign_raw(message);
        println!("  - Our signature: {}", hex::encode(signature.to_bytes()));

        // Verify our signature manually
        let verifying_key = self.public_key();
        println!("  - Public key: {}", hex::encode(verifying_key.to_bytes()));

        signature
    }
}

impl ExtendedPublicKey {
    fn derive_child(&self, index: u32) -> Option<ExtendedPublicKey> {
        if index >= (1 << 31) {
            return None; // Only non-hardened
        }

        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(&self.public_key.to_bytes());
        hmac_input.extend_from_slice(&index.to_le_bytes());

        let mut hmac = HmacSha512::new_from_slice(&self.chain_code).ok()?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();

        let zl = &z[..28];

        // Compute child public key = parent_public_key + [8 * ZL] * B
        let mut zl_padded = [0u8; 32];
        zl_padded[..28].copy_from_slice(zl);

        // Multiply by 8
        let mut carry = 0u8;
        for i in 0..32 {
            let temp = (zl_padded[i] as u16) << 3 | carry as u16;
            zl_padded[i] = temp as u8;
            carry = (temp >> 8) as u8;
        }

        let scalar = Scalar::from_bytes_mod_order(zl_padded);
        let point_to_add = scalar * ED25519_BASEPOINT_POINT;

        // Add to parent public key
        let compressed = CompressedEdwardsY::from_slice(&self.public_key.to_bytes()).ok()?;
        let parent_point = compressed.decompress()?;
        let child_point = parent_point + point_to_add;

        // Check if result is identity point
        if child_point == EdwardsPoint::identity() {
            return None;
        }

        let child_public_key = VerifyingKey::from_bytes(&child_point.compress().as_bytes()).ok()?;

        Some(ExtendedPublicKey {
            public_key: child_public_key,
            chain_code: self.chain_code,
        })
    }
}

// Query to index mapping
fn query_to_start_index(query: &str) -> u32 {
    let mut hasher = Sha256::new();
    hasher.update(query.as_bytes());
    let hash = hasher.finalize();
    
    let mut bytes = [0u8; 4];
    bytes.copy_from_slice(&hash[..4]);
    let index = u32::from_le_bytes(bytes);
    
    index & 0x1FFFFFFF
}

// Find working child key for query
fn find_working_child_private(root: &ExtendedPrivateKey, query: &str, max_tries: u32) -> Option<(ExtendedPrivateKey, u32)> {
    let start_index = query_to_start_index(query);

    for i in 0..max_tries {
        let index = start_index.wrapping_add(i);
        if index >= (1 << 31) {
            continue; // Skip hardened indices
        }

        if let Some(child) = root.derive_child(index) {
            return Some((child, index));
        }
    }
    None
}

fn find_working_child_public(root: &ExtendedPublicKey, query: &str, max_tries: u32) -> Option<(ExtendedPublicKey, u32)> {
    let start_index = query_to_start_index(query);

    for i in 0..max_tries {
        let index = start_index.wrapping_add(i);
        if index >= (1 << 31) {
            continue; // Skip hardened indices
        }

        if let Some(child) = root.derive_child(index) {
            return Some((child, index));
        }
    }
    None
}

// Publisher class
pub struct Publisher {
    root_keypair: ExtendedPrivateKey,
    max_search: u32,
}

impl Publisher {
    pub fn new() -> Self {
        let root_keypair = ExtendedPrivateKey::generate();
        Publisher {
            root_keypair,
            max_search: 1000,
        }
    }

    pub fn from_seed(seed: &[u8]) -> Self {
        let root_keypair = ExtendedPrivateKey::from_seed(seed);
        Publisher {
            root_keypair,
            max_search: 1000,
        }
    }
    
    pub fn root_public_key(&self) -> VerifyingKey {
        self.root_keypair.public_key()
    }
    
    pub async fn publish_root_record(&self) -> Result<(), Box<dyn std::error::Error>> {
        let root_record = RootRecord {
            chain_code: hex::encode(&self.root_keypair.chain_code),
            version: 1,
        };

        // Create TXT record data
        let txt_data = format!("v=1;chain={}", root_record.chain_code);

        // Create records
        let records = vec![
            ("_pkarr".to_string(), txt_data.clone(), 300),
        ];

        // Create signed packet using our manual signing
        let packet_bytes = self.root_keypair.create_signed_packet(records)?;

        // Submit to pkarr relay
        let pkarr_key = self.root_keypair.to_pkarr_public_key();
        let result = submit_to_pkarr_relay(&pkarr_key, &packet_bytes).await;

        match result {
            Ok(_) => {
                println!("✅ Published root record to pkarr network!");
                println!("Root record data: {}", txt_data);
                println!("Root pkarr key: {}", pkarr_key.to_z32());
                println!("Test at: https://app.pkarr.org/{}", pkarr_key.to_z32());
            },
            Err(e) => {
                println!("❌ Failed to publish root record: {}", e);
                return Err(e);
            }
        }

        Ok(())
    }
    
    pub async fn publish_data(&self, query: &str, data: &str) -> Result<VerifyingKey, Box<dyn std::error::Error>> {
        // Find working child key for this query
        let (child_keypair, index) = find_working_child_private(&self.root_keypair, query, self.max_search)
            .ok_or("No working child key found")?;

        let child_public = child_keypair.public_key();

        // Create TXT record with data
        let txt_data = format!("query={};data={};index={}", query, data, index);

        // Create records
        let records = vec![
            ("_data".to_string(), txt_data.clone(), 300),
        ];

        // Create signed packet using our manual signing
        let packet_bytes = child_keypair.create_signed_packet(records)?;

        // Submit to pkarr relay
        let child_pkarr_key = child_keypair.to_pkarr_public_key();
        let result = submit_to_pkarr_relay(&child_pkarr_key, &packet_bytes).await;

        match result {
            Ok(_) => {
                println!("✅ Published data '{}' for query '{}' to pkarr network!", data, query);
                println!("Data record: {}", txt_data);
                println!("Child Ed25519 key: {} (z32: {})",
                         hex::encode(child_public.to_bytes()),
                         z32::encode(&child_public.to_bytes()));
                println!("Child pkarr key: {}", child_pkarr_key.to_z32());
                println!("Test at: https://app.pkarr.org/{}", child_pkarr_key.to_z32());
            },
            Err(e) => {
                println!("❌ Failed to publish data: {}", e);
                return Err(e);
            }
        }

        Ok(child_public)
    }
}

// Client class
pub struct Client {
    root_public: ExtendedPublicKey,
    max_search: u32,
}

impl Client {
    pub async fn new(root_public_key: VerifyingKey) -> Result<Self, Box<dyn std::error::Error>> {
        // Resolve the chain code from the root pkarr record
        let root_pkarr_key = PkarrPublicKey::from(root_public_key);
        let pkarr_client_raw = pkarr::Client::builder().build()?;

        println!("🔍 Resolving root record from pkarr key: {}", root_pkarr_key.to_z32());

        let chain_code = if let Some(signed_packet) = pkarr_client_raw.resolve(&root_pkarr_key).await {
            println!("✅ Found root pkarr record!");

            // Look for _pkarr TXT record with chain code
            let mut found_chain_code = None;
            for record in signed_packet.resource_records("_pkarr") {
                if let pkarr::dns::rdata::RData::TXT(txt_data) = &record.rdata {
                    let txt_string: String = txt_data.clone().try_into()
                        .map_err(|e| format!("Failed to convert TXT to string: {:?}", e))?;

                    println!("Found root TXT record: {}", txt_string);

                    // Parse chain=<hex> from the record
                    if let Some(chain_start) = txt_string.find("chain=") {
                        let chain_part = &txt_string[chain_start + 6..];
                        let chain_hex = if let Some(chain_end) = chain_part.find(';') {
                            &chain_part[..chain_end]
                        } else {
                            chain_part
                        };

                        if let Ok(chain_bytes) = hex::decode(chain_hex) {
                            if chain_bytes.len() == 32 {
                                let mut chain_code_array = [0u8; 32];
                                chain_code_array.copy_from_slice(&chain_bytes);
                                found_chain_code = Some(chain_code_array);
                                println!("✅ Resolved chain code from pkarr: {}", chain_hex);
                                break;
                            }
                        }
                    }
                }
            }

            found_chain_code.ok_or("Chain code not found in root pkarr record")?
        } else {
            println!("❌ No root pkarr record found, using deterministic chain code");
            // Fallback to deterministic chain code generation
            let mut chain_hasher = Sha256::new();
            chain_hasher.update(b"BIP32-Ed25519-chain");
            chain_hasher.update(&root_public_key.to_bytes());
            chain_hasher.finalize().into()
        };

        let root_public = ExtendedPublicKey {
            public_key: root_public_key,
            chain_code,
        };

        Ok(Client {
            root_public,
            max_search: 1000,
        })
    }

    pub async fn query_data(&self, query: &str) -> Result<Option<String>, Box<dyn std::error::Error>> {
        // Find working child public key for this query
        let (child_public, _index) = find_working_child_public(&self.root_public, query, self.max_search)
            .ok_or("No working child key found")?;

        println!("Child public key: {}", z32::encode(&child_public.public_key.to_bytes()));

        // Convert to pkarr public key
        let child_pkarr_key = PkarrPublicKey::from(child_public.public_key);

        // Actually resolve from pkarr network
        let pkarr_client = pkarr::Client::builder().build()?;
        println!("🔍 Resolving data for query '{}' from pkarr key: {}", query, child_pkarr_key.to_z32());
        println!("Child Ed25519 key: {} (z32: {})",
                 hex::encode(child_public.public_key.to_bytes()),
                 z32::encode(&child_public.public_key.to_bytes()));

        if let Some(signed_packet) = pkarr_client.resolve(&child_pkarr_key).await {
            println!("✅ Found pkarr record!");

            // Look for _data TXT record
            for record in signed_packet.resource_records("_data") {
                if let pkarr::dns::rdata::RData::TXT(txt_data) = &record.rdata {
                    let txt_string: String = txt_data.clone().try_into()
                        .map_err(|e| format!("Failed to convert TXT to string: {:?}", e))?;

                    println!("Found TXT record: {}", txt_string);

                    // Parse data=value from the record
                    if let Some(data_start) = txt_string.find("data=") {
                        let data_part = &txt_string[data_start + 5..];
                        let data_value = if let Some(data_end) = data_part.find(';') {
                            &data_part[..data_end]
                        } else {
                            data_part
                        };

                        println!("✅ Resolved data: {}", data_value);
                        return Ok(Some(data_value.to_string()));
                    }
                }
            }

            println!("❌ No _data record found in pkarr response");
            Ok(None)
        } else {
            println!("❌ No pkarr record found for key: {}", child_pkarr_key.to_z32());
            Ok(None)
        }
    }

    pub fn derive_child_public(&self, query: &str) -> Option<VerifyingKey> {
        let (child_public, _index) = find_working_child_public(&self.root_public, query, self.max_search)?;
        Some(child_public.public_key)
    }
}

// Submit to pkarr network using our manually signed packet
async fn submit_to_pkarr_relay(
    pkarr_key: &PkarrPublicKey,
    packet_bytes: &[u8],
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🌐 Submitting to pkarr network:");
    println!("  - pkarr key: {}", pkarr_key.to_z32());
    println!("  - Packet size: {} bytes", packet_bytes.len());

    // Create a SignedPacket from our manually signed bytes
    let signed_packet = pkarr::SignedPacket::from_bytes(packet_bytes)?;

    // Use pkarr client to publish
    let client = pkarr::Client::builder().build()?;
    client.publish(&signed_packet, None).await?;

    println!("✅ Successfully published to pkarr network!");
    Ok(())
}

// Test signature verification against pkarr's expectations
fn test_signature_verification() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing signature verification:");
    
    let key = ExtendedPrivateKey::generate();
    let message = b"test message for pkarr";
    
    // Create raw signature
    let raw_sig = key.sign_raw(message);
    
    // Try to verify with ed25519-dalek for comparison
    let verifying_key = key.public_key();
    
    println!("  - Public key: {}", hex::encode(verifying_key.to_bytes()));
    println!("  - Message: {}", String::from_utf8_lossy(message));
    println!("  - Raw signature: {}", hex::encode(raw_sig.to_bytes()));
    
    // Manual verification
    let r_compressed = CompressedEdwardsY::from_slice(&raw_sig.r).unwrap();
    let r_point = r_compressed.decompress().unwrap();
    let s_scalar = Scalar::from_bytes_mod_order(raw_sig.s);
    
    // Compute k = hash(R || A || M)
    let mut hasher = Sha512::new();
    hasher.update(&raw_sig.r);
    hasher.update(&verifying_key.to_bytes());
    hasher.update(message);
    let k_hash = hasher.finalize();
    let k_scalar = Scalar::from_bytes_mod_order_wide(&k_hash.try_into().unwrap());
    
    // Parse A point
    let a_compressed = CompressedEdwardsY::from_slice(&verifying_key.to_bytes()).unwrap();
    let a_point = a_compressed.decompress().unwrap();
    
    // Verify: S * B = R + k * A
    let left = s_scalar * ED25519_BASEPOINT_POINT;
    let right = r_point + (k_scalar * a_point);
    
    if left == right {
        println!("✅ Manual signature verification successful!");
        Ok(())
    } else {
        println!("❌ Manual signature verification failed!");
        Err("Signature verification failed".into())
    }
}

// Example usage
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Test signature verification first
    test_signature_verification()?;

    // Publisher workflow
    println!("\n=== Publisher Workflow ===");
    let publisher = Publisher::new();
    let root_pub = publisher.root_public_key();

    let root_pkarr_key = PkarrPublicKey::from(root_pub);
    println!("Root Ed25519 key: {} (z32: {})",
             hex::encode(root_pub.to_bytes()),
             z32::encode(&root_pub.to_bytes()));
    println!("Root pkarr key: {}", root_pkarr_key.to_z32());

    // Publish root record
    publisher.publish_root_record().await?;

    // Show deterministic index mapping
    println!("\n=== Index Mapping Verification ===");
    let hello_start = query_to_start_index("hello");
    let foo_start = query_to_start_index("foo");
    println!("Query 'hello' deterministically maps to start index: {}", hello_start);
    println!("Query 'foo' deterministically maps to start index: {}", foo_start);

    // Verify deterministic behavior - same query should always give same index
    let hello_start2 = query_to_start_index("hello");
    let foo_start2 = query_to_start_index("foo");
    assert_eq!(hello_start, hello_start2, "Query mapping should be deterministic!");
    assert_eq!(foo_start, foo_start2, "Query mapping should be deterministic!");
    println!("✅ Verified: Query mapping is deterministic");

    // Publish some data
    println!("\n=== Publishing Data ===");
    let _child_pub1 = publisher.publish_data("hello", "world").await?;
    let _child_pub2 = publisher.publish_data("foo", "bar").await?;

    // Wait for network propagation
    println!("\n⏳ Waiting 5 seconds for pkarr network propagation...");
    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

    // Client workflow
    println!("\n=== Client Workflow ===");
    println!("Using root Ed25519 key: {} (z32: {})",
             hex::encode(root_pub.to_bytes()),
             z32::encode(&root_pub.to_bytes()));
    println!("Using root pkarr key: {}", root_pkarr_key.to_z32());
    let client = Client::new(root_pub).await?;

    // Query data
    if let Some(data) = client.query_data("hello").await? {
        println!("Query 'hello' returned: {}", data);
    }

    if let Some(data) = client.query_data("foo").await? {
        println!("Query 'foo' returned: {}", data);
    }

    // Test child key derivation and verify indices match
    println!("\n=== Verification: Client vs Server Key Derivation ===");

    // Test "hello" query
    if let Some(client_child_pub) = client.derive_child_public("hello") {
        let client_child_pkarr_key = PkarrPublicKey::from(client_child_pub);
        println!("Client derived 'hello' key: {} (z32: {})",
                 hex::encode(client_child_pub.to_bytes()),
                 z32::encode(&client_child_pub.to_bytes()));
        println!("Client derived pkarr key: {}", client_child_pkarr_key.to_z32());

        // Now derive the same key on server side to verify they match
        if let Some((server_child_priv, server_index)) = find_working_child_private(&publisher.root_keypair, "hello", publisher.max_search) {
            let server_child_pub = server_child_priv.public_key();
            println!("Server derived 'hello' key: {} (z32: {}) at index: {}",
                     hex::encode(server_child_pub.to_bytes()),
                     z32::encode(&server_child_pub.to_bytes()),
                     server_index);

            // Verify they match
            if client_child_pub.to_bytes() == server_child_pub.to_bytes() {
                println!("✅ SUCCESS: Client and server derived the SAME key for 'hello'!");
            } else {
                println!("❌ ERROR: Client and server derived DIFFERENT keys for 'hello'!");
            }

            // Show the index derivation process
            let start_index = query_to_start_index("hello");
            println!("Query 'hello' maps to start index: {}", start_index);
            println!("Found working key at offset: {} (final index: {})", server_index - start_index, server_index);
        }
    }

    println!();

    // Test "foo" query
    if let Some(client_child_pub) = client.derive_child_public("foo") {
        let _client_child_pkarr_key = PkarrPublicKey::from(client_child_pub);
        println!("Client derived 'foo' key: {} (z32: {})",
                 hex::encode(client_child_pub.to_bytes()),
                 z32::encode(&client_child_pub.to_bytes()));

        // Now derive the same key on server side to verify they match
        if let Some((server_child_priv, server_index)) = find_working_child_private(&publisher.root_keypair, "foo", publisher.max_search) {
            let server_child_pub = server_child_priv.public_key();
            println!("Server derived 'foo' key: {} (z32: {}) at index: {}",
                     hex::encode(server_child_pub.to_bytes()),
                     z32::encode(&server_child_pub.to_bytes()),
                     server_index);

            // Verify they match
            if client_child_pub.to_bytes() == server_child_pub.to_bytes() {
                println!("✅ SUCCESS: Client and server derived the SAME key for 'foo'!");
            } else {
                println!("❌ ERROR: Client and server derived DIFFERENT keys for 'foo'!");
            }

            // Show the index derivation process
            let start_index = query_to_start_index("foo");
            println!("Query 'foo' maps to start index: {}", start_index);
            println!("Found working key at offset: {} (final index: {})", server_index - start_index, server_index);
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_mapping() {
        let query = "test_query";
        let index1 = query_to_start_index(query);
        let index2 = query_to_start_index(query);

        assert_eq!(index1, index2); // Deterministic
        assert!(index1 < (1 << 31)); // Non-hardened

        println!("Query '{}' maps to index: {}", query, index1);
    }

    #[test]
    fn test_key_derivation() {
        let root = ExtendedPrivateKey::generate();
        let root_pub = root.public();

        let query = "test_query";

        // Test private key derivation
        let (child_priv, index) = find_working_child_private(&root, query, 100).unwrap();

        // Test public key derivation
        let (child_pub, index2) = find_working_child_public(&root_pub, query, 100).unwrap();

        assert_eq!(index, index2);
        assert_eq!(child_priv.public_key().to_bytes(), child_pub.public_key.to_bytes());

        println!("Found working child at index: {}", index);
    }

    #[test]
    fn test_packet_creation() {
        let key = ExtendedPrivateKey::generate();
        let records = vec![
            ("_test".to_string(), "hello=world".to_string(), 300),
        ];

        let packet = key.create_signed_packet(records).unwrap();

        // Packet should be: 64 bytes signature + DNS body
        assert!(packet.len() > 64);
        assert_eq!(&packet[..64], &key.sign_raw(&packet[64..]).to_bytes());
    }
}