use curve25519_dalek::{scalar::<PERSON><PERSON><PERSON>, traits::Identity};
use curve25519_dalek::edwards::{EdwardsPoint, CompressedEdwardsY};
use curve25519_dalek::constants::ED25519_BASEPOINT_POINT;
use ed25519_dalek::VerifyingKey;
use hmac::{Hmac, Mac};
use sha2::{Sha256, Sha512, Digest};
use pkarr::{dns, PublicKey as PkarrPublicKey, SignedPacket};
use std::str::FromStr;

type HmacSha512 = Hmac<Sha512>;

// Raw Ed25519 signature implementation
#[derive(Debug, Clone)]
pub struct RawSignature {
    pub r: [u8; 32],
    pub s: [u8; 32],
}

impl RawSignature {
    pub fn to_bytes(&self) -> [u8; 64] {
        let mut bytes = [0u8; 64];
        bytes[..32].copy_from_slice(&self.r);
        bytes[32..].copy_from_slice(&self.s);
        bytes
    }
}

// BIP32-Ed25519 implementation (keeping it focused)
#[derive(Clone)]
struct ExtendedPrivateKey {
    kl: [u8; 32],
    kr: [u8; 32],
    chain_code: [u8; 32],
}

impl ExtendedPrivateKey {
    fn generate() -> Self {
        let seed: [u8; 32] = rand::random();
        Self::from_seed(&seed)
    }
    
    fn from_seed(seed: &[u8]) -> Self {
        loop {
            let mut hasher = Sha512::new();
            hasher.update(seed);
            hasher.update(&rand::random::<[u8; 4]>());
            let k = hasher.finalize();
            
            let mut kl = [0u8; 32];
            let mut kr = [0u8; 32];
            kl.copy_from_slice(&k[..32]);
            kr.copy_from_slice(&k[32..]);
            
            if (kl[31] & 0x20) != 0 {
                continue;
            }
            
            kl[0] &= 0xF8;
            kl[31] &= 0x7F;
            kl[31] |= 0x40;
            
            let public_key = {
                let scalar = Scalar::from_bytes_mod_order(kl);
                let point = scalar * ED25519_BASEPOINT_POINT;
                VerifyingKey::from_bytes(&point.compress().as_bytes()).unwrap()
            };

            let mut chain_hasher = Sha256::new();
            chain_hasher.update(b"BIP32-Ed25519-chain");
            chain_hasher.update(&public_key.to_bytes());
            let chain_code = chain_hasher.finalize().into();
            
            return ExtendedPrivateKey { kl, kr, chain_code };
        }
    }
    
    fn derive_child(&self, index: u32) -> Option<ExtendedPrivateKey> {
        if index >= (1 << 31) {
            return None;
        }
        
        let public_key = self.public_key();
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(&public_key.to_bytes());
        hmac_input.extend_from_slice(&index.to_le_bytes());
        
        let mut hmac = HmacSha512::new_from_slice(&self.chain_code).ok()?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();
        
        let zl = &z[..28];
        let zr = &z[32..];
        
        let mut kl_child = [0u8; 32];
        kl_child[..28].copy_from_slice(zl);
        
        let mut carry = 0u8;
        for i in 0..32 {
            let temp = (kl_child[i] as u16) << 3 | carry as u16;
            kl_child[i] = temp as u8;
            carry = (temp >> 8) as u8;
        }
        
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = kl_child[i] as u16 + self.kl[i] as u16 + carry;
            kl_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        let mut kr_child = [0u8; 32];
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = zr[i] as u16 + self.kr[i] as u16 + carry;
            kr_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        let scalar = Scalar::from_bytes_mod_order(kl_child);
        if scalar == Scalar::ZERO {
            return None;
        }
        
        Some(ExtendedPrivateKey {
            kl: kl_child,
            kr: kr_child,
            chain_code: self.chain_code,
        })
    }
    
    fn public_key(&self) -> VerifyingKey {
        let scalar = Scalar::from_bytes_mod_order(self.kl);
        let point = scalar * ED25519_BASEPOINT_POINT;
        VerifyingKey::from_bytes(&point.compress().as_bytes()).unwrap()
    }
    
    fn to_pkarr_public_key(&self) -> PkarrPublicKey {
        PkarrPublicKey::try_from(self.public_key().to_bytes()).unwrap()
    }
    
    // RAW Ed25519 signature that matches pkarr's expectations
    fn sign_raw(&self, message: &[u8]) -> RawSignature {
        // Step 1: Compute r = hash(kR || M) mod q
        let mut hasher = Sha512::new();
        hasher.update(&self.kr);
        hasher.update(message);
        let r_hash = hasher.finalize();
        let r_scalar = Scalar::from_bytes_mod_order_wide(&r_hash.try_into().unwrap());
        
        // Step 2: Compute R = r * B
        let r_point = r_scalar * ED25519_BASEPOINT_POINT;
        let r_bytes = r_point.compress().as_bytes().clone();
        
        // Step 3: Compute S = (r + hash(R || A || M) * s) mod q
        let public_key = self.public_key();
        let mut hasher = Sha512::new();
        hasher.update(&r_bytes);
        hasher.update(&public_key.to_bytes());
        hasher.update(message);
        let k_hash = hasher.finalize();
        let k_scalar = Scalar::from_bytes_mod_order_wide(&k_hash.try_into().unwrap());
        
        let s_scalar = Scalar::from_bytes_mod_order(self.kl);
        let s_final = r_scalar + (k_scalar * s_scalar);
        
        RawSignature {
            r: r_bytes,
            s: s_final.as_bytes().clone(),
        }
    }
    
    // Create a proper pkarr SignedPacket using our raw signature
    fn create_signed_packet(&self, records: Vec<(String, String, u32)>) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        use dns::op::{Header, Message, MessageType, OpCode};
        use dns::rr::{Name, Record, RecordType};
        use dns::rdata::RData;
        
        // Create DNS message
        let mut message = Message::new();
        let mut header = Header::new();
        header.set_message_type(MessageType::Response);
        header.set_op_code(OpCode::Query);
        header.set_authoritative(true);
        message.set_header(header);
        
        // Add records
        for (name, value, ttl) in records {
            let record_name = Name::from_str(&name)?;
            let txt_data = dns::rdata::TXT::new(vec![value]);
            let record = Record::from_rdata(record_name, ttl, RData::TXT(txt_data));
            message.add_answer(record);
        }
        
        // Serialize DNS message to bytes
        let mut packet_bytes = Vec::new();
        message.emit(&mut packet_bytes)?;
        
        // Remove the header (first 12 bytes) as pkarr signs only the body
        let packet_body = &packet_bytes[12..];
        
        // Sign the packet body
        let raw_signature = self.sign_raw(packet_body);
        
        // Create the final packet: signature (64 bytes) + packet body
        let mut final_packet = Vec::new();
        final_packet.extend_from_slice(&raw_signature.to_bytes());
        final_packet.extend_from_slice(packet_body);
        
        println!("📦 Created packet:");
        println!("  - Signature: {} bytes", 64);
        println!("  - Body: {} bytes", packet_body.len());
        println!("  - Total: {} bytes", final_packet.len());
        println!("  - Signature hex: {}", hex::encode(&raw_signature.to_bytes()));
        
        Ok(final_packet)
    }
}

// Query to index mapping
fn query_to_start_index(query: &str) -> u32 {
    let mut hasher = Sha256::new();
    hasher.update(query.as_bytes());
    let hash = hasher.finalize();
    
    let mut bytes = [0u8; 4];
    bytes.copy_from_slice(&hash[..4]);
    let index = u32::from_le_bytes(bytes);
    
    index & 0x1FFFFFFF
}

// Find working child key
fn find_working_child_private(root: &ExtendedPrivateKey, query: &str, max_tries: u32) -> Option<(ExtendedPrivateKey, u32)> {
    let start_index = query_to_start_index(query);
    
    for i in 0..max_tries {
        let index = start_index.wrapping_add(i);
        if index >= (1 << 31) {
            continue;
        }
        
        if let Some(child) = root.derive_child(index) {
            return Some((child, index));
        }
    }
    None
}

// Publisher with proper pkarr integration
pub struct RawPublisher {
    root_keypair: ExtendedPrivateKey,
    max_search: u32,
}

impl RawPublisher {
    pub fn new() -> Self {
        let root_keypair = ExtendedPrivateKey::generate();
        RawPublisher {
            root_keypair,
            max_search: 1000,
        }
    }
    
    pub fn root_public_key(&self) -> VerifyingKey {
        self.root_keypair.public_key()
    }
    
    pub async fn publish_root_record(&self) -> Result<(), Box<dyn std::error::Error>> {
        let chain_code_hex = hex::encode(&self.root_keypair.chain_code);
        let txt_data = format!("v=1;chain={}", chain_code_hex);
        
        println!("🚀 Publishing root record:");
        println!("  - Chain code: {}", chain_code_hex);
        println!("  - TXT data: {}", txt_data);
        
        // Create records
        let records = vec![
            ("_pkarr".to_string(), txt_data, 300),
        ];
        
        // Create signed packet
        let packet_bytes = self.root_keypair.create_signed_packet(records)?;
        
        // Submit to pkarr relay
        let pkarr_key = self.root_keypair.to_pkarr_public_key();
        let result = submit_to_pkarr_relay(&pkarr_key, &packet_bytes).await;
        
        match result {
            Ok(_) => {
                println!("✅ Root record published successfully!");
                println!("  - pkarr key: {}", pkarr_key.to_z32());
                println!("  - Test at: https://app.pkarr.org/{}", pkarr_key.to_z32());
            },
            Err(e) => {
                println!("❌ Failed to publish root record: {}", e);
                return Err(e);
            }
        }
        
        Ok(())
    }
    
    pub async fn publish_data(&self, query: &str, data: &str) -> Result<VerifyingKey, Box<dyn std::error::Error>> {
        let (child_keypair, index) = find_working_child_private(&self.root_keypair, query, self.max_search)
            .ok_or("No working child key found")?;
        
        let child_public = child_keypair.public_key();
        let txt_data = format!("query={};data={};index={}", query, data, index);
        
        println!("🚀 Publishing data:");
        println!("  - Query: {}", query);
        println!("  - Data: {}", data);
        println!("  - Index: {}", index);
        println!("  - TXT data: {}", txt_data);
        
        // Create records
        let records = vec![
            ("_data".to_string(), txt_data, 300),
        ];
        
        // Create signed packet
        let packet_bytes = child_keypair.create_signed_packet(records)?;
        
        // Submit to pkarr relay
        let child_pkarr_key = child_keypair.to_pkarr_public_key();
        let result = submit_to_pkarr_relay(&child_pkarr_key, &packet_bytes).await;
        
        match result {
            Ok(_) => {
                println!("✅ Data published successfully!");
                println!("  - Child pkarr key: {}", child_pkarr_key.to_z32());
                println!("  - Test at: https://app.pkarr.org/{}", child_pkarr_key.to_z32());
            },
            Err(e) => {
                println!("❌ Failed to publish data: {}", e);
                return Err(e);
            }
        }
        
        Ok(child_public)
    }
}

// Proper pkarr relay submission
async fn submit_to_pkarr_relay(
    pkarr_key: &PkarrPublicKey,
    packet_bytes: &[u8],
) -> Result<(), Box<dyn std::error::Error>> {
    use reqwest;
    
    let client = reqwest::Client::new();
    let url = format!("https://pkarr.relay.org/{}", pkarr_key.to_z32());
    
    println!("🌐 Submitting to pkarr relay:");
    println!("  - URL: {}", url);
    println!("  - Packet size: {} bytes", packet_bytes.len());
    
    let response = client
        .put(&url)
        .header("Content-Type", "application/octet-stream")
        .body(packet_bytes.to_vec())
        .send()
        .await?;
    
    let status = response.status();
    let response_text = response.text().await?;
    
    println!("📡 Response:");
    println!("  - Status: {}", status);
    println!("  - Body: {}", response_text);
    
    if status.is_success() {
        Ok(())
    } else {
        Err(format!("HTTP {}: {}", status, response_text).into())
    }
}

// Test signature verification against pkarr's expectations
fn test_signature_verification() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing signature verification:");
    
    let key = ExtendedPrivateKey::generate();
    let message = b"test message for pkarr";
    
    // Create raw signature
    let raw_sig = key.sign_raw(message);
    
    // Try to verify with ed25519-dalek for comparison
    let verifying_key = key.public_key();
    
    println!("  - Public key: {}", hex::encode(verifying_key.to_bytes()));
    println!("  - Message: {}", String::from_utf8_lossy(message));
    println!("  - Raw signature: {}", hex::encode(raw_sig.to_bytes()));
    
    // Manual verification
    let r_compressed = CompressedEdwardsY::from_slice(&raw_sig.r).unwrap();
    let r_point = r_compressed.decompress().unwrap();
    let s_scalar = Scalar::from_bytes_mod_order(raw_sig.s);
    
    // Compute k = hash(R || A || M)
    let mut hasher = Sha512::new();
    hasher.update(&raw_sig.r);
    hasher.update(&verifying_key.to_bytes());
    hasher.update(message);
    let k_hash = hasher.finalize();
    let k_scalar = Scalar::from_bytes_mod_order_wide(&k_hash.try_into().unwrap());
    
    // Parse A point
    let a_compressed = CompressedEdwardsY::from_slice(&verifying_key.to_bytes()).unwrap();
    let a_point = a_compressed.decompress().unwrap();
    
    // Verify: S * B = R + k * A
    let left = s_scalar * ED25519_BASEPOINT_POINT;
    let right = r_point + (k_scalar * a_point);
    
    if left == right {
        println!("✅ Manual signature verification successful!");
        Ok(())
    } else {
        println!("❌ Manual signature verification failed!");
        Err("Signature verification failed".into())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== BIP32-Ed25519 + pkarr Integration Test ===\n");
    
    // Test signature verification first
    test_signature_verification()?;
    
    println!("\n=== Publisher Test ===");
    let publisher = RawPublisher::new();
    let root_pub = publisher.root_public_key();
    
    println!("Root public key: {}", hex::encode(root_pub.to_bytes()));
    
    // Test publishing root record
    println!("\n--- Publishing Root Record ---");
    match publisher.publish_root_record().await {
        Ok(_) => println!("✅ Root record publishing completed"),
        Err(e) => println!("❌ Root record publishing failed: {}", e),
    }
    
    // Wait for propagation
    println!("\n⏳ Waiting 3 seconds for propagation...");
    tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
    
    // Test publishing data
    println!("\n--- Publishing Data ---");
    match publisher.publish_data("hello", "world").await {
        Ok(_) => println!("✅ Data publishing completed"),
        Err(e) => println!("❌ Data publishing failed: {}", e),
    }
    
    // Test second data record
    match publisher.publish_data("test", "value").await {
        Ok(_) => println!("✅ Second data publishing completed"),
        Err(e) => println!("❌ Second data publishing failed: {}", e),
    }
    
    println!("\n=== Test Complete ===");
    println!("Check the URLs printed above to verify the records are accessible!");
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_packet_creation() {
        let key = ExtendedPrivateKey::generate();
        let records = vec![
            ("_test".to_string(), "hello=world".to_string(), 300),
        ];
        
        let packet = key.create_signed_packet(records).unwrap();
        
        // Packet should be: 64 bytes signature + DNS body
        assert!(packet.len() > 64);
        assert_eq!(&packet[..64], &key.sign_raw(&packet[64..]).to_bytes());
    }
    
    #[test]
    fn test_key_derivation() {
        let root = ExtendedPrivateKey::generate();
        let query = "test";
        
        let (child, index) = find_working_child_private(&root, query, 100).unwrap();
        
        // Should be deterministic
        let (child2, index2) = find_working_child_private(&root, query, 100).unwrap();
        assert_eq!(index, index2);
        assert_eq!(child.public_key().to_bytes(), child2.public_key().to_bytes());
    }
}