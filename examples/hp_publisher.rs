use anyhow::Result;
use std::collections::HashMap;
use iroh_topic_tracker::integrations::hp::{HpClient, generate_keypair};

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Pkarr Publisher Example ===\n");

    // Step 1: Generate a new keypair for this publisher
    let keypair = generate_keypair();
    println!("1. Generated new pkarr keypair");
    println!("   Root pkarr public key: {}", keypair.public_key().to_z32());
    println!("   (Save this key for the client example)\n");

    // Step 2: Create the HP client
    let hp_client = HpClient::new()?;
    println!("2. Created HP client\n");

    // Step 3: Prepare key-value pairs to publish
    let mut key_value_pairs = HashMap::new();
    key_value_pairs.insert("hello".to_string(), "Hello World from Hierarchical Pkarr!".to_string());
    key_value_pairs.insert("timestamp".to_string(), chrono::Utc::now().to_rfc3339());
    key_value_pairs.insert("version".to_string(), "1.0.0".to_string());
    key_value_pairs.insert("author".to_string(), "HP Publisher Example".to_string());

    println!("3. Prepared key-value pairs to publish:");
    for (key, value) in &key_value_pairs {
        println!("   {}: {}", key, value);
    }
    println!();

    // Step 4: Write the query key with the data
    let query = "hello-world";
    println!("4. Publishing data under query: '{}'", query);
    
    match hp_client.write_query_key(&keypair, query, key_value_pairs.clone()).await {
        Ok(query_pkarr_key) => {
            println!("   ✓ Successfully published data!");
            println!("   Query-specific pkarr key: {}", query_pkarr_key.to_z32());
        }
        Err(e) => {
            println!("   ✗ Failed to publish data: {}", e);
            return Err(e.into());
        }
    }
    println!();

    // Step 5: Publish additional data under a different query
    let mut user_data = HashMap::new();
    user_data.insert("name".to_string(), "Alice".to_string());
    user_data.insert("role".to_string(), "Publisher".to_string());
    user_data.insert("status".to_string(), "active".to_string());

    let user_query = "user-data";
    println!("5. Publishing additional data under query: '{}'", user_query);
    
    match hp_client.write_query_key(&keypair, user_query, user_data.clone()).await {
        Ok(user_pkarr_key) => {
            println!("   ✓ Successfully published user data!");
            println!("   User data pkarr key: {}", user_pkarr_key.to_z32());
        }
        Err(e) => {
            println!("   ✗ Failed to publish user data: {}", e);
            return Err(e.into());
        }
    }
    println!();

    // Step 6: Summary
    println!("=== Publication Summary ===");
    println!("Root pkarr key: {}", keypair.public_key().to_z32());
    println!("Published queries:");
    println!("  - '{}': {} key-value pairs", query, key_value_pairs.len());
    println!("  - '{}': {} key-value pairs", user_query, user_data.len());
    println!();
    println!("To resolve this data, run the client example with:");
    println!("cargo run --example hp_client {}", keypair.public_key().to_z32());
    println!();
    println!("The hierarchical pkarr system has:");
    println!("1. ✓ Generated a root keypair");
    println!("2. ✓ Derived and published a query master key (if needed)");
    println!("3. ✓ Derived query-specific keys for each query");
    println!("4. ✓ Published data under the derived keys");
    println!();
    println!("Clients can now resolve this data using only the root public key!");

    Ok(())
}
