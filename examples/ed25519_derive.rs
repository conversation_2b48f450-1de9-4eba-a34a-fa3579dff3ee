use sha2::{Sha256, <PERSON>ha512, Digest};
use hmac::{Hma<PERSON>, <PERSON>};
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON>ey};
use curve25519_dalek::{scalar::<PERSON>alar, constants::ED25519_BASEPOINT_POINT};

type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, Clone)]
pub struct ExtendedKey {
    pub key_left: [u8; 32],   // kL - left 32 bytes (scalar)
    pub key_right: [u8; 32],  // kR - right 32 bytes
    pub chain_code: [u8; CHAIN_CODE_LENGTH],
    pub public_key: VerifyingKey,
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidSeed,
    InvalidChildIndex,
    InvalidKey,
    KeyAtInfinity,
    CryptoError,
}

    fn main() {
        // Example for Pkarr-style key derivation
        // Try different seeds until we find one that works (doesn't have third highest bit set)
        let mut counter = 0u32;
        let root_key = loop {
            let mut seed = b"pkarr test seed".to_vec();
            seed.extend_from_slice(&counter.to_le_bytes());

            match ExtendedKey::from_seed(&seed) {
                Ok(key) => {
                    println!("Found valid key with counter: {}", counter);
                    break key;
                }
                Err(KeyDerivationError::InvalidKey) => {
                    println!("Invalid key with counter {}, trying next...", counter);
                    counter += 1;
                    if counter > 1000 {
                        panic!("Could not find valid key after 1000 attempts");
                    }
                }
                Err(e) => panic!("Unexpected error: {:?}", e),
            }
        };

        // Derive keys for different purposes
        let identity_key = root_key.derive_path("m/0'").unwrap();
        let signing_key = root_key.derive_path("m/1'").unwrap();

        println!("Identity key: {}", hex::encode(identity_key.public_key.as_bytes()));
        println!("Signing key: {}", hex::encode(signing_key.public_key.as_bytes()));
    }

impl ExtendedKey {
    /// Generate root extended key from seed
    pub fn from_seed(seed: &[u8]) -> Result<Self, KeyDerivationError> {
        // Generate master secret from seed
        let mut hmac = HmacSha512::new_from_slice(b"ed25519 seed")
            .map_err(|_| KeyDerivationError::InvalidSeed)?;
        hmac.update(seed);
        let master_secret = hmac.finalize().into_bytes();
        
        let master_key = &master_secret[..32];
        Self::from_master_key(master_key)
    }
    
    /// Generate root extended key from 32-byte master key
    pub fn from_master_key(master_key: &[u8]) -> Result<Self, KeyDerivationError> {
        println!("Master key: {}", master_key.len()
    );
        if master_key.len() != 32 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        // Hash the master key to get extended key
        let mut hasher = Sha512::new();
        hasher.update(master_key);
        let extended = hasher.finalize();
        
        let mut key_left = [0u8; 32];
        let mut key_right = [0u8; 32];
        key_left.copy_from_slice(&extended[..32]);
        key_right.copy_from_slice(&extended[32..]);

        // Check third highest bit requirement BEFORE clamping
        // According to BIP32-Ed25519, if the third highest bit of the last byte of kL is set, discard the key
        println!("Key left before clamping: {}", key_left[31] & 0x20);
        if (key_left[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }

        // Apply Ed25519 bit manipulations to kL
        Self::clamp_scalar(&mut key_left);
        
        // Generate public key
        let scalar = Scalar::from_bytes_mod_order(key_left);
        let public_point = &scalar * &ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate chain code
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(master_key);
        let chain_code_hash = chain_hasher.finalize();
        let mut chain_code = [0u8; CHAIN_CODE_LENGTH];
        chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left,
            key_right,
            chain_code,
            public_key,
        })
    }
    
    /// Derive child key at given index
    pub fn derive_child(&self, index: u32) -> Result<ExtendedKey, KeyDerivationError> {
        let is_hardened = index >= HARDENED_OFFSET;
        
        // Prepare HMAC input
        let mut hmac_input = Vec::new();
        
        if is_hardened {
            // Hardened derivation: 0x00 || kL || kR || index
            hmac_input.push(0x00);
            hmac_input.extend_from_slice(&self.key_left);
            hmac_input.extend_from_slice(&self.key_right);
        } else {
            // Non-hardened derivation: 0x02 || A || index
            hmac_input.push(0x02);
            hmac_input.extend_from_slice(self.public_key.as_bytes());
        }
        hmac_input.extend_from_slice(&index.to_le_bytes());
        
        // Compute Z = HMAC-SHA512(chain_code, input)
        let mut hmac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();
        
        // Split Z into ZL (28 bytes) and ZR (32 bytes)
        let z_left = &z[..28];
        let z_right = &z[32..];
        
        // Derive child kL = 8*ZL + parent_kL
        let mut z_left_32 = [0u8; 32];
        z_left_32[..28].copy_from_slice(z_left);
        
        let z_scalar = Scalar::from_bytes_mod_order(z_left_32);
        let eight_z_scalar = z_scalar * Scalar::from(8u64);
        let parent_scalar = Scalar::from_bytes_mod_order(self.key_left);
        let child_scalar = eight_z_scalar + parent_scalar;
        
        // Check if child scalar is zero (should discard)
        if child_scalar == Scalar::ZERO {
            return Err(KeyDerivationError::InvalidChildIndex);
        }
        
        let child_key_left = child_scalar.to_bytes();
        
        // Derive child kR = ZR + parent_kR (mod 2^256)
        let mut child_key_right = [0u8; 32];
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = self.key_right[i] as u16 + z_right[i] as u16 + carry;
            child_key_right[i] = sum as u8;
            carry = sum >> 8;
        }
        
        // Generate child public key
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        
        // Check if public key is identity point (should discard)
        if child_public_point.compress().to_bytes() == [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] {
            return Err(KeyDerivationError::KeyAtInfinity);
        }
        
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate child chain code
        let mut chain_input = Vec::new();
        if is_hardened {
            chain_input.push(0x01);
            chain_input.extend_from_slice(&self.key_left);
            chain_input.extend_from_slice(&self.key_right);
        } else {
            chain_input.push(0x03);
            chain_input.extend_from_slice(self.public_key.as_bytes());
        }
        chain_input.extend_from_slice(&index.to_le_bytes());
        
        let mut chain_hmac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        chain_hmac.update(&chain_input);
        let chain_result = chain_hmac.finalize().into_bytes();
        
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_result[32..]);
        
        Ok(ExtendedKey {
            key_left: child_key_left,
            key_right: child_key_right,
            chain_code: child_chain_code,
            public_key: child_public_key,
        })
    }
    
    /// Derive child key from derivation path (e.g., "m/44'/0'/0'/0/0")
    pub fn derive_path(&self, path: &str) -> Result<ExtendedKey, KeyDerivationError> {
        let path = path.strip_prefix("m/").unwrap_or(path);
        let mut current_key = self.clone();
        
        for component in path.split('/') {
            if component.is_empty() {
                continue;
            }
            
            let (index_str, is_hardened) = if component.ends_with('\'') || component.ends_with('h') {
                (&component[..component.len()-1], true)
            } else {
                (component, false)
            };
            
            let index: u32 = index_str.parse()
                .map_err(|_| KeyDerivationError::InvalidChildIndex)?;
            
            let final_index = if is_hardened {
                index + HARDENED_OFFSET
            } else {
                index
            };
            
            current_key = current_key.derive_child(final_index)?;
        }
        
        Ok(current_key)
    }
    
    /// Get signing key for this extended key
    pub fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.key_left)
    }
    
    /// Apply Ed25519 scalar clamping
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;  // Clear lowest 3 bits
        scalar[31] &= 127; // Clear highest bit
        scalar[31] |= 64;  // Set second highest bit
    }
}

// Example usage and tests
#[cfg(test)]
mod tests {
    use super::*;
    use hex;
    
    #[test]
    fn test_key_derivation() {
        let seed = hex::decode("000102030405060708090a0b0c0d0e0f").unwrap();
        let root_key = ExtendedKey::from_seed(&seed).unwrap();
        
        println!("Root public key: {}", hex::encode(root_key.public_key.as_bytes()));
        
        // Derive child key m/0'
        let child_key = root_key.derive_child(HARDENED_OFFSET).unwrap();
        println!("Child m/0' public key: {}", hex::encode(child_key.public_key.as_bytes()));
        
        // Derive using path
        let path_key = root_key.derive_path("m/44'/0'/0'/0/0").unwrap();
        println!("Path m/44'/0'/0'/0/0 public key: {}", hex::encode(path_key.public_key.as_bytes()));
    }
    
    #[test]
    fn test_pkarr_compatible_derivation() {
        // Example for Pkarr-style key derivation
        // Use a seed that we know works (doesn't have third highest bit set)
        let seed = hex::decode("000102030405060708090a0b0c0d0e0f").unwrap();
        let root_key = ExtendedKey::from_seed(&seed).unwrap();

        // Derive keys for different purposes
        let identity_key = root_key.derive_path("m/0'").unwrap();
        let signing_key = root_key.derive_path("m/1'").unwrap();

        println!("Identity key: {}", hex::encode(identity_key.public_key.as_bytes()));
        println!("Signing key: {}", hex::encode(signing_key.public_key.as_bytes()));
    }
}

// Cargo.toml dependencies needed:
/*
[dependencies]
ed25519-dalek = "2.0"
curve25519-dalek = "4.0"
sha2 = "0.10"
hmac = "0.12"
hex = "0.4"
*/