use anyhow::Result;
use ed25519_dalek::SigningKey;
use pkarr::{Keypair, PublicKey};
use hex;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Key Derivation Comparison Test ===\n");

    // Test 1: Create a signing key and see how pkar<PERSON> handles it
    let test_secret = [1u8; 32]; // Simple test secret
    println!("1. Testing with simple secret key:");
    println!("   Secret bytes: {}", hex::encode(&test_secret));
    
    // Create Ed25519 signing key
    let ed25519_signing_key = SigningKey::from_bytes(&test_secret);
    let ed25519_public_key = ed25519_signing_key.verifying_key();
    
    println!("   Ed25519 public key: {}", hex::encode(ed25519_public_key.to_bytes()));
    
    // Create pkarr keypair from same secret
    let pkarr_keypair = Keypair::from_secret_key(&test_secret);
    println!("   Pkarr public key: {}", hex::encode(pkarr_keypair.public_key().as_bytes()));
    
    // Try direct conversion
    let pkarr_public_direct = PublicKey::try_from(&ed25519_public_key.to_bytes())?;
    println!("   Direct conversion: {}", hex::encode(pkarr_public_direct.as_bytes()));
    
    println!("   Keys match (keypair vs direct): {}", 
             pkarr_keypair.public_key().as_bytes() == pkarr_public_direct.as_bytes());
    println!();

    // Test 2: Check if the issue is in the secret key format
    println!("2. Testing secret key formats:");
    
    // Get the secret key from pkarr keypair
    let pkarr_secret = pkarr_keypair.secret_key();
    println!("   Pkarr secret key: {}", hex::encode(pkarr_secret));
    println!("   Original secret: {}", hex::encode(&test_secret));
    println!("   Secrets match: {}", pkarr_secret == test_secret);
    println!();

    // Test 3: Check if Ed25519 and pkarr use different key derivation
    println!("3. Testing key derivation differences:");
    
    // Create another Ed25519 key from pkarr's secret
    let ed25519_from_pkarr = SigningKey::from_bytes(&pkarr_secret);
    let ed25519_public_from_pkarr = ed25519_from_pkarr.verifying_key();
    
    println!("   Ed25519 from pkarr secret: {}", hex::encode(ed25519_public_from_pkarr.to_bytes()));
    println!("   Matches original Ed25519: {}", 
             ed25519_public_from_pkarr.to_bytes() == ed25519_public_key.to_bytes());
    println!();

    // Test 4: Check the actual bytes being used
    println!("4. Detailed byte comparison:");
    println!("   Ed25519 public bytes: {:?}", ed25519_public_key.to_bytes());
    println!("   Pkarr public bytes:   {:?}", pkarr_keypair.public_key().as_bytes());
    println!("   Direct conv bytes:    {:?}", pkarr_public_direct.as_bytes());
    println!();

    // Test 5: Test our hierarchical derivation
    println!("5. Testing hierarchical derivation:");

    use iroh_topic_tracker::integrations::hp::{ExtendedKey, DeterministicKeyFinder};

    // Create an extended key from our test secret
    let extended_key = ExtendedKey::from_seed(&test_secret)?;
    println!("   Extended key public: {}", hex::encode(extended_key.public_key().as_bytes()));

    // Compare with direct Ed25519 key
    let direct_ed25519 = SigningKey::from_bytes(&test_secret);
    println!("   Direct Ed25519 public: {}", hex::encode(direct_ed25519.verifying_key().to_bytes()));
    println!("   Extended vs Direct match: {}",
             extended_key.public_key().as_bytes() == &direct_ed25519.verifying_key().to_bytes());

    // Test child derivation
    let (index, child_key) = DeterministicKeyFinder::find_valid_child_key(&extended_key, "test", false)?;
    println!("   Child key index: {}", index);
    println!("   Child public key: {}", hex::encode(child_key.public_key().as_bytes()));

    // Test pkarr conversion of child key
    let child_pkarr_keypair = Keypair::from_secret_key(&child_key.signing_key().to_bytes());
    let child_pkarr_direct = PublicKey::try_from(&child_key.public_key().to_bytes())?;

    println!("   Child pkarr (keypair): {}", hex::encode(child_pkarr_keypair.public_key().as_bytes()));
    println!("   Child pkarr (direct):  {}", hex::encode(child_pkarr_direct.as_bytes()));
    println!("   Child pkarr keys match: {}",
             child_pkarr_keypair.public_key().as_bytes() == child_pkarr_direct.as_bytes());

    Ok(())
}
