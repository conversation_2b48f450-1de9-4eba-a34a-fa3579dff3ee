use anyhow::Result;
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON><PERSON>};
use pkarr::Keypair;
use sha2::{Digest, Sha256};
use hmac::{Hmac, Mac};
use curve25519_dalek::{scalar::<PERSON>alar, constants::ED25519_BASEPOINT_POINT};

type HmacSha512 = Hmac<sha2::Sha512>;

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Key Derivation Pipeline ===\n");
    
    // =============================================
    // STEP 1: Create Root Keypair
    // =============================================
    println!("📍 STEP 1: Create Root Keypair");
    let root_pkarr_keypair = Keypair::random();
    let root_seed = root_pkarr_keypair.secret_key();
    
    println!("   Root Pkarr Public Key: {}", root_pkarr_keypair.public_key().to_z32());
    println!("   Root Seed: {}", hex::encode(&root_seed)[..32].to_string() + "...");
    
    // =============================================
    // STEP 2: Hardened Derive Query Master Key
    // =============================================
    println!("\n📍 STEP 2: Hardened Derive Query Master Key");
    
    // Find valid root extended key
    let (_root_counter, root_extended_key) = DeterministicKeyFinder::find_valid_root_key(
        &root_seed, 
        "root"
    )?;
    
    // Hardened derivation: requires private key
    let (query_master_index, query_master_extended_key) = DeterministicKeyFinder::find_valid_child_key(
        &root_extended_key, 
        "query-master", 
        true  // HARDENED = true
    )?;
    
    println!("   Query Master Index: {} (hardened)", query_master_index);
    println!("   Query Master Private Key: {}", hex::encode(&query_master_extended_key.signing_key().to_bytes())[..32].to_string() + "...");
    println!("   Query Master Public Key:  {}", hex::encode(query_master_extended_key.public_key.as_bytes()));
    
    // =============================================
    // STEP 3: Non-Hardened Derive Purpose Key (Method 1: From Private Key)
    // =============================================
    println!("\n📍 STEP 3: Non-Hardened Derive Purpose Key (Method 1: From Private Key)");
    
    let purpose = "user-data";
    let (purpose_index_1, purpose_extended_key_1) = DeterministicKeyFinder::find_valid_child_key(
        &query_master_extended_key, 
        purpose, 
        false  // NON-HARDENED = false
    )?;
    
    println!("   Purpose: '{}'", purpose);
    println!("   Purpose Index: {} (non-hardened)", purpose_index_1);
    println!("   Purpose Private Key: {}", hex::encode(&purpose_extended_key_1.signing_key().to_bytes())[..32].to_string() + "...");
    println!("   Purpose Public Key:  {}", hex::encode(purpose_extended_key_1.public_key.as_bytes()));
    
    // =============================================
    // STEP 4: Non-Hardened Derive Purpose Key (Method 2: From Public Key Only)
    // =============================================
    println!("\n📍 STEP 4: Non-Hardened Derive Purpose Key (Method 2: From Public Key Only)");
    
    // Simulate client scenario: only has query master PUBLIC key
    let query_master_public_key_only = query_master_extended_key.public_key;
    
    // Client-side derivation: only uses public key
    let (purpose_index_2, purpose_public_key_2) = derive_public_key_only(
        &query_master_public_key_only,
        &query_master_extended_key.chain_code,
        purpose
    )?;
    
    println!("   Purpose: '{}'", purpose);
    println!("   Purpose Index: {} (non-hardened)", purpose_index_2);
    println!("   Purpose Private Key: [UNKNOWN - Client cannot derive]");
    println!("   Purpose Public Key:  {}", hex::encode(purpose_public_key_2.as_bytes()));
    
    // =============================================
    // STEP 5: Assert Equality - The Math Works!
    // =============================================
    println!("\n📍 STEP 5: Assert Equality - The Math Works!");
    
    // Verify that both derivation methods produce the same result
    assert_eq!(purpose_index_1, purpose_index_2, "Purpose indexes should match");
    assert_eq!(purpose_extended_key_1.public_key.as_bytes(), purpose_public_key_2.as_bytes(), "Purpose public keys should match");
    
    println!("   ✅ Index Match: {} == {}", purpose_index_1, purpose_index_2);
    println!("   ✅ Public Key Match: Both methods produce identical public keys");
    println!("   ✅ Mathematical Consistency: (parent_private + offset) × G == parent_public + (offset × G)");
    
    // =============================================
    // STEP 6: Demonstrate Security Properties
    // =============================================
    println!("\n📍 STEP 6: Demonstrate Security Properties");
    
    println!("   🔒 Server (has private keys):");
    println!("      • Can derive query master private key ✅");
    println!("      • Can derive purpose private keys ✅");
    println!("      • Can sign with any derived key ✅");
    
    println!("   🔓 Client (has public keys only):");
    println!("      • Cannot derive query master private key ❌ (hardened derivation)");
    println!("      • Cannot derive purpose private keys ❌ (no parent private key)");
    println!("      • Can derive purpose public keys ✅ (non-hardened derivation)");
    println!("      • Can resolve records ✅");
    println!("      • Cannot forge records ❌ (no private keys)");
    
    // =============================================
    // STEP 7: Show Key Derivation Paths
    // =============================================
    println!("\n📍 STEP 7: Key Derivation Paths");
    
    println!("   Key Hierarchy:");
    println!("   m (root)");
    println!("   └─ m/{}' (query-master, hardened)", query_master_index);
    println!("      └─ m/{}'/{}  (user-data, non-hardened)", query_master_index, purpose_index_1);
    
    println!("\n   Legend:");
    println!("   ' = hardened derivation (requires private key)");
    println!("   no ' = non-hardened derivation (public key derivation possible)");
    
    println!("\n✅ Pipeline Complete! The mathematical foundation is proven.");
    
    Ok(())
}

// Client-side public key derivation (no private keys needed)
fn derive_public_key_only(
    parent_public_key: &VerifyingKey,
    parent_chain_code: &[u8; 32],
    purpose: &str,
) -> Result<(u32, VerifyingKey), KeyDerivationError> {
    
    // Use the same deterministic method as server
    let max_attempts = 1000;
    let mut hasher = Sha256::new();
    hasher.update(purpose.as_bytes());
    let hash = hasher.finalize();
    let start_index = u32::from_le_bytes([hash[0], hash[1], hash[2], hash[3]]);
    
    for attempt in 0..max_attempts {
        let index = start_index.wrapping_add(attempt);
        
        // Create HMAC input for non-hardened derivation
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02); // Non-hardened prefix
        hmac_input.extend_from_slice(parent_public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        // Generate offset using parent's chain code
        let mut mac = HmacSha512::new_from_slice(parent_chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        // Extract offset (left 28 bytes, same as server)
        let mut offset_bytes = [0u8; 32];
        offset_bytes[..28].copy_from_slice(&hmac_result[..28]);
        
        // Clamp the offset for Ed25519 compatibility
        offset_bytes[0] &= 248;
        offset_bytes[31] &= 127;
        offset_bytes[31] |= 64;
        
        let offset_scalar = Scalar::from_bytes_mod_order(offset_bytes);
        
        // Derive child public key: parent_public + (offset × G)
        let parent_point = parent_public_key.as_bytes();
        let parent_point = curve25519_dalek::edwards::CompressedEdwardsY(*parent_point)
            .decompress()
            .ok_or(KeyDerivationError::CryptoError)?;
        
        let offset_point = &offset_scalar * &ED25519_BASEPOINT_POINT;
        let child_point = parent_point + offset_point;
        
        // Check if this produces a valid key
        if let Some(compressed) = child_point.compress().as_bytes().try_into().ok() {
            if let Ok(child_public_key) = VerifyingKey::from_bytes(&compressed) {
                return Ok((index, child_public_key));
            }
        }
    }
    
    Err(KeyDerivationError::InvalidKey)
}

// ... (Include the ExtendedKey and DeterministicKeyFinder implementations from previous code)