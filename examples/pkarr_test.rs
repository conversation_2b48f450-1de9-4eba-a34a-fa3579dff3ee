use anyhow::Result;

#[tokio::main]
async fn main() -> Result<()> {
    println!("Testing pkarr directly...");
    
    // Test pkarr key generation
    let keypair = pkarr::Keypair::random();
    println!("✓ Generated keypair: {}", keypair.public_key().to_z32());
    
    // Test parsing a known key
    let test_key = "ej1b6zrud5q1ufrib1dkzx38hx473wyx5o4ouj4qpu5ut1pegmfo";
    println!("Testing key parsing: {}", test_key);
    let pub_key = pkarr::PublicKey::try_from(test_key)?;
    println!("✓ Parsed key: {}", pub_key.to_z32());
    
    Ok(())
}
