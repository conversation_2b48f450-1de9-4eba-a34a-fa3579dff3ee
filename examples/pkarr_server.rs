use anyhow::Result;
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON>ey};
use iroh_topic_tracker::integrations::pkarr::PkarrClient;
use pkarr::Keypair;

// We'll include the key derivation code here for now
// In a real implementation, this would be in a shared module

use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};


type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, <PERSON>lone)]
pub struct ExtendedKey {
    key_left: [u8; 32],
    key_right: [u8; 32],
    chain_code: [u8; CHAIN_CODE_LENGTH],
    public_key: VerifyingKey,
}

#[derive(Debug)]
pub enum KeyDerivationError {
    Invalid<PERSON>ey,
    InvalidChildIndex,
    CryptoError,
    KeyAtInfinity,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::InvalidChildIndex => write!(f, "Invalid child index"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
            KeyDerivationError::KeyAtInfinity => write!(f, "Key at infinity"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl ExtendedKey {
    /// Create extended key from seed
    pub fn from_seed(seed: &[u8]) -> Result<Self, KeyDerivationError> {
        // Generate master key using HMAC-SHA512
        let mut mac = HmacSha512::new_from_slice(b"ed25519 seed")
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(seed);
        let master_key = mac.finalize().into_bytes();
        
        println!("Master key: {}", master_key[31] & 0x20);
        
        // Split into left and right parts
        let mut extended = [0u8; 64];
        extended.copy_from_slice(&master_key);
        
        let mut key_left = [0u8; 32];
        let mut key_right = [0u8; 32];
        key_left.copy_from_slice(&extended[..32]);
        key_right.copy_from_slice(&extended[32..]);
        
        // Check third highest bit requirement BEFORE clamping
        // According to BIP32-Ed25519, if the third highest bit of the last byte of kL is set, discard the key
        println!("Key left before clamping: {}", key_left[31] & 0x20);
        if (key_left[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        // Apply Ed25519 bit manipulations to kL
        Self::clamp_scalar(&mut key_left);
        
        // Generate public key
        let scalar = Scalar::from_bytes_mod_order(key_left);
        let public_point = &scalar * &ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate chain code
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&master_key);
        let chain_code_hash = chain_hasher.finalize();
        let mut chain_code = [0u8; CHAIN_CODE_LENGTH];
        chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left,
            key_right,
            chain_code,
            public_key,
        })
    }
    
    /// Derive child key at given index
    pub fn derive_child(&self, index: u32) -> Result<ExtendedKey, KeyDerivationError> {
        let is_hardened = index >= HARDENED_OFFSET;
        
        // Prepare HMAC input
        let mut hmac_input = Vec::new();
        
        if is_hardened {
            // Hardened derivation: 0x00 || kL || kR || index
            hmac_input.push(0x00);
            hmac_input.extend_from_slice(&self.key_left);
            hmac_input.extend_from_slice(&self.key_right);
        } else {
            // Non-hardened derivation: 0x02 || A || index
            hmac_input.push(0x02);
            hmac_input.extend_from_slice(self.public_key.as_bytes());
        }
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        // Compute HMAC
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        // Split result
        let mut kl_new = [0u8; 32];
        let mut kr_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        kr_new.copy_from_slice(&hmac_result[32..]);
        
        // Check third highest bit requirement BEFORE clamping
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        // Apply Ed25519 bit manipulations
        Self::clamp_scalar(&mut kl_new);
        
        // Generate child scalar and public key
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        
        // Generate child public key
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        
        // Check if public key is identity point (should discard)
        if child_public_point.compress().to_bytes() == [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] {
            return Err(KeyDerivationError::KeyAtInfinity);
        }
        
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate child chain code
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left: kl_new,
            key_right: kr_new,
            chain_code: child_chain_code,
            public_key: child_public_key,
        })
    }
    
    /// Derive child key from derivation path (e.g., "m/44'/0'/0'/0/0")
    pub fn derive_path(&self, path: &str) -> Result<ExtendedKey, KeyDerivationError> {
        let path = path.strip_prefix("m/").unwrap_or(path);
        let mut current_key = self.clone();
        
        for component in path.split('/') {
            if component.is_empty() {
                continue;
            }
            
            let (index_str, is_hardened) = if component.ends_with('\'') || component.ends_with('h') {
                (&component[..component.len()-1], true)
            } else {
                (component, false)
            };
            
            let index: u32 = index_str.parse()
                .map_err(|_| KeyDerivationError::InvalidChildIndex)?;
            
            let final_index = if is_hardened {
                index + HARDENED_OFFSET
            } else {
                index
            };
            
            current_key = current_key.derive_child(final_index)?;
        }
        
        Ok(current_key)
    }
    
    /// Get the signing key for this extended key
    pub fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.key_left)
    }
    
    /// Apply Ed25519 scalar clamping
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;  // Clear bottom 3 bits
        scalar[31] &= 127; // Clear top bit
        scalar[31] |= 64;  // Set second highest bit
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Pkarr Server-side Workflow ===");
    
    // Step 1: Start from a pkarr keypair (or generate one)
    let pkarr_keypair = Keypair::random();
    println!("1. Generated pkarr keypair: {}", pkarr_keypair.public_key().to_z32());
    
    // Step 2: Generate query keypair using key derivation
    // Use the pkarr secret key as seed for deterministic derivation
    let seed = pkarr_keypair.secret_key();
    
    // Try different counters until we find a valid key
    let mut counter = 0u32;
    let root_key = loop {
        let mut extended_seed = seed.to_vec();
        extended_seed.extend_from_slice(&counter.to_le_bytes());
        
        match ExtendedKey::from_seed(&extended_seed) {
            Ok(key) => {
                println!("2. Found valid root key with counter: {}", counter);
                break key;
            }
            Err(KeyDerivationError::InvalidKey) => {
                println!("   Invalid key with counter {}, trying next...", counter);
                counter += 1;
                if counter > 1000 {
                    return Err(anyhow::anyhow!("Could not find valid key after 1000 attempts"));
                }
            }
            Err(e) => return Err(anyhow::anyhow!("Unexpected error: {:?}", e)),
        }
    };
    
    // Derive the query key using a specific path
    // Try different derivation paths until we find one that works
    let mut derivation_index = 0u32;
    let query_key = loop {
        let path = format!("m/{}'", derivation_index);
        match root_key.derive_path(&path) {
            Ok(key) => {
                println!("3. Found valid query key with derivation path: {}", path);
                break key;
            }
            Err(KeyDerivationError::InvalidKey) => {
                println!("   Invalid query key with path {}, trying next...", path);
                derivation_index += 1;
                if derivation_index > 100 {
                    return Err(anyhow::anyhow!("Could not find valid query key after 100 attempts"));
                }
            }
            Err(e) => return Err(anyhow::anyhow!("Unexpected error during derivation: {:?}", e)),
        }
    };

    let query_public_key = query_key.public_key;
    let _query_signing_key = query_key.signing_key();
    
    println!("4. Derived query public key: {}", hex::encode(query_public_key.as_bytes()));
    
    // Step 3: Publish query public key to pkarr DNS
    let pkarr_client = PkarrClient::new()?;
    let published_key = pkarr_client.publish_query_key(
        &pkarr_keypair,
        &query_public_key,
        Some("query"), // subdomain
    ).await?;

    println!("5. Successfully published to pkarr!");
    println!("   Pkarr public key: {}", published_key.to_z32());
    println!("   Query public key: {}", hex::encode(query_public_key.as_bytes()));
    
    // Optional: Test resolution immediately
    println!("\n=== Testing Resolution ===");
    match pkarr_client.resolve_query_key(&published_key, Some("query")).await {
        Ok(resolved_key) => {
            println!("✓ Successfully resolved query key: {}", hex::encode(resolved_key.as_bytes()));
            if resolved_key.as_bytes() == query_public_key.as_bytes() {
                println!("✓ Resolved key matches published key!");
            } else {
                println!("✗ Resolved key does not match published key!");
            }
        }
        Err(e) => {
            println!("✗ Failed to resolve query key: {}", e);
        }
    }
    
    Ok(())
}
