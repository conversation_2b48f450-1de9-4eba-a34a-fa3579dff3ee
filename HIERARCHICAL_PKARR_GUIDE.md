# Hierarchical Pkarr Key Management Guide

## Overview

This guide explains the hierarchical pkarr key management system that enables secure, deterministic key derivation for distributed applications. The system allows a server to publish data under derived keys while clients can resolve that data using only public keys.

## Core Concepts

### 1. Key Hierarchy Structure

```
Root Pkarr Keypair (server only)
└── Query Master Key (m/X' - hardened)
    ├── Purpose Key 1 (m/X'/Y - non-hardened)
    ├── Purpose Key 2 (m/X'/Z - non-hardened)
    └── Purpose Key N (m/X'/W - non-hardened)
```

### 2. Security Model

- **Server Security**: Has root private key → can derive all keys and publish/update records
- **Client Capabilities**: Has root public key → can derive purpose public keys and resolve records
- **Protection**: Hardened derivation prevents clients from deriving private keys

## Key Components

### ExtendedKey Structure

The `ExtendedKey` struct represents a hierarchical deterministic key:

```rust
pub struct ExtendedKey {
    key_left: [u8; 32],     // Private key material (left half)
    key_right: [u8; 32],    // Private key material (right half)  
    chain_code: [u8; 32],   // Chain code for derivation
    public_key: Verifying<PERSON>ey, // Ed25519 public key
}
```

**Purpose**: Enables hierarchical key derivation following BIP32-style patterns for Ed25519 keys.

**Why each field**:
- `key_left`: Left 32 bytes of HMAC output, used as Ed25519 private key material
- `key_right`: Right 32 bytes of HMAC output, used for additional entropy in hardened derivation
- `chain_code`: Used as HMAC key for child derivation, enables deterministic key tree
- `public_key`: Ed25519 public key corresponding to the private key material

### PublicExtendedKey Structure

```rust
pub struct PublicExtendedKey {
    public_key: VerifyingKey,   // Ed25519 public key
    chain_code: [u8; 32],       // Chain code for public derivation
}
```

**Purpose**: Enables public key derivation without access to private key material.

**Why each field**:
- `public_key`: Ed25519 public key for cryptographic operations
- `chain_code`: Enables non-hardened child derivation using only public information

### Key Derivation Functions

#### `from_seed(seed: &[u8])`
- **Purpose**: Creates root extended key from seed material
- **Process**: Uses HMAC-SHA512 with "ed25519 seed" to generate master key
- **Security**: Validates key material and clamps scalar for Ed25519 compatibility

#### `derive_child(index: u32)`
- **Purpose**: Derives child keys from parent keys
- **Hardened vs Non-hardened**:
  - Hardened (index ≥ 0x80000000): Uses private key material, prevents public derivation
  - Non-hardened (index < 0x80000000): Uses public key, allows public derivation
- **Process**: HMAC-SHA512 with parent chain code and key material

### Deterministic Key Finding

#### `DeterministicKeyFinder`
**Purpose**: Solves the "invalid key" problem in Ed25519 derivation where some derived keys are cryptographically invalid.

#### `find_valid_key(query: &str, derive_fn: F)`
- **Purpose**: Finds first valid key for a given query string
- **Process**: 
  1. Hash query string with SHA256
  2. Use hash as deterministic starting index
  3. Try sequential indices until valid key found
- **Benefits**: Same query always produces same key (reproducible)

### Pkarr Integration

#### `SimplePkarrClient`
**Purpose**: Handles publishing and resolving Ed25519 public keys via pkarr DNS records.

#### `publish_query_key(pkarr_keypair, query_public_key, chain_code, subdomain)`
- **Purpose**: Publishes Ed25519 public key and chain code as TXT record in pkarr DNS
- **Format**: `ed25519=<hex_public_key>:chain=<hex_chain_code>`
- **Process**: Creates signed DNS packet and publishes to pkarr network
- **Why chain code**: Enables clients to perform public key derivation

#### `resolve_query_key(pkarr_public_key, subdomain)`
- **Purpose**: Resolves Ed25519 public key and chain code from pkarr DNS record
- **Returns**: `PublicExtendedKey` containing both public key and chain code
- **Process**: Queries pkarr network and parses TXT record for both components

#### `publish_hello_world(keypair, message)`
- **Purpose**: Publishes arbitrary data under a specific pkarr key
- **Format**: Standard TXT record with message content
- **Use case**: Demonstrates publishing application data under derived keys

#### `resolve_hello_world(public_key)`
- **Purpose**: Resolves application data from a specific pkarr key
- **Process**: Queries pkarr network for TXT records under "hello" subdomain
- **Use case**: Demonstrates client resolution of application data

## Workflow Explanation

### Server Workflow

1. **Generate Root Keypair**: Create pkarr keypair for DNS publishing
2. **Derive Query Master**: Use deterministic finding to get valid hardened child key
3. **Publish Query Master**: Store query master public key in pkarr DNS
4. **Derive Purpose Keys**: Create non-hardened children for specific purposes
5. **Publish Data**: Use purpose keys to publish application data

### Client Workflow

1. **Obtain Root Public Key**: Get server's pkarr public key (out of band)
2. **Resolve Query Master**: Fetch query master public key from pkarr DNS
3. **Derive Purpose Keys**: Use public key derivation to get purpose public keys
4. **Resolve Data**: Query pkarr records using derived public keys

## Security Properties

### What Clients Can Do
- Derive purpose public keys from query master public key
- Resolve any published records using derived keys
- Verify authenticity of resolved data

### What Clients Cannot Do
- Derive any private keys (hardened protection)
- Publish or overwrite any records (no private key access)
- Access data not intended for them

## Use Cases

### Social Applications
- **Posts**: Derive key for user posts
- **Messages**: Derive key for private messages  
- **Profile**: Derive key for profile information
- **Friends**: Derive key for friend lists

### File Storage
- **Documents**: Derive key for document storage
- **Images**: Derive key for image storage
- **Backups**: Derive key for backup data
- **Shared**: Derive key for shared files

### Identity Systems
- **Credentials**: Derive key for verifiable credentials
- **Attestations**: Derive key for attestations
- **Proofs**: Derive key for cryptographic proofs
- **Metadata**: Derive key for identity metadata

## Implementation Benefits

1. **Deterministic**: Same inputs always produce same outputs
2. **Hierarchical**: Organized key structure with clear relationships
3. **Secure**: Clients cannot derive private keys
4. **Scalable**: Unlimited purpose-specific keys
5. **Interoperable**: Works with standard pkarr infrastructure

## Running the Example

The accompanying example file `hierarchical_pkarr_standalone.rs` demonstrates the complete workflow:

### Server Mode
```bash
cargo run --example hierarchical_pkarr_standalone server
```

This will:
1. Generate a root pkarr keypair
2. Derive a query master key using deterministic finding
3. Publish the query master public key to pkarr DNS
4. Derive a specific "hello-world" query key
5. Publish "Hello World from Hierarchical Pkarr!" under that key
6. Display the root pkarr key for client use

### Client Mode
```bash
cargo run --example hierarchical_pkarr_standalone client <root_pkarr_key>
```

This will:
1. Use the provided root pkarr public key
2. Resolve the query master public key from pkarr DNS
3. Derive the same "hello-world" query key (public key only)
4. Resolve and display the "Hello World" message

### Example Workflow
```bash
# Terminal 1: Run server
$ cargo run --example hierarchical_pkarr_standalone server
=== SERVER MODE ===
1. Root pkarr keypair: 6q4oy7sssccbp845o4m9rsfjif77q7ga8mw5ib1p5pn89hygtcuo
2. Query master key derived at index: 123
3. ✓ Published query master public key to pkarr
4. Hello world key derived at index: 456
5. ✓ Published 'Hello World' under query key: abc123...

📋 For client mode, use:
   Root pkarr key: 6q4oy7sssccbp845o4m9rsfjif77q7ga8mw5ib1p5pn89hygtcuo

# Terminal 2: Run client
$ cargo run --example hierarchical_pkarr_standalone client 6q4oy7sssccbp845o4m9rsfjif77q7ga8mw5ib1p5pn89hygtcuo
=== CLIENT MODE ===
1. Using root pkarr key: 6q4oy7sssccbp845o4m9rsfjif77q7ga8mw5ib1p5pn89hygtcuo
2. ✓ Resolved query master public key and chain code from pkarr
3. ✓ Derived hello world public key at index: 456
4. ✓ Resolved hello world message: 'Hello World from Hierarchical Pkarr!'
```

This demonstrates the complete security model where the server can publish data under derived keys, and clients can resolve that data using only public key derivation.

### Important Notes

- **Pkarr Propagation**: Records may take time to propagate across the pkarr network. The client includes retry logic to handle this.
- **Network Dependency**: Both server and client modes require internet connectivity to interact with the pkarr network.
- **Deterministic Behavior**: The same query strings will always produce the same derived keys, ensuring reproducible results.
- **Security**: Clients can derive public keys but never private keys, maintaining the security model.
