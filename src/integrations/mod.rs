mod iroh_gossip_int;
mod pkarr_int;
mod hp_lib;

#[cfg(feature = "iroh-gossip-auto-discovery")]
pub mod iroh_gossip {
    pub use super::iroh_gossip_int::{AutoDiscoveryBuilder, AutoDiscoveryGossip, AutoDiscoveryNew,GossipAutoDiscovery};
}

pub mod pkarr {
    pub use super::pkarr_int::{PkarrClient, PublicExtendedKey, signing_key_to_pkarr_keypair, verifying_key_to_pkarr_public_key};
}

pub mod hp {
    pub use super::hp_lib::{
        HpClient, HpError, ExtendedKey, HpPublicExtendedKey, DeterministicKeyFinder,
        generate_keypair
    };
    // Re-export the pkarr conversion functions from the original module
    pub use super::pkarr_int::{signing_key_to_pkarr_keypair, verifying_key_to_pkarr_public_key};
}
